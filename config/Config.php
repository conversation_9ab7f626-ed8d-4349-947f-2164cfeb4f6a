<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @version:     1.1.0
 * @create date: 2022-01-30
 * @modify date: 2022-03-11
 * @description: Provides a get function and sets the api url for a used configuration
 */
 
// no direct access
defined('GM_EXEC') or die('Access denied');

class Config
{

    // shop name (shopify only)
    protected $shopName = '';

    // city for getting the location id (shopify only)
    protected $city = '';

    // location id (shopify only)
    protected $locationId = '';

    // create only products with images (shopify only)
    protected $needImage = false;

    // url to the live site (shopify only)
    protected $liveUrl = '';

    // update also title and descriptions? (shopify only)
    protected $updateAll = true;

    // after a product was created update the stock amount too (shopify only)
    protected $updateStock = false;

    // account id (korona only)
    protected $accountId = '';

    // api username (korona only)
    protected $apiUsername = '';

    // organization name (korona only)
    protected $organizationName = '';

    // organization id (korona only)
    protected $organizationId = '';

    // cache log data into var (korona only)
    protected $cacheLogging = true;

    // update also not changed items (korona only)
    protected $forceUpdate = false;

    // send report after update (korona only)
    protected $sendReport = true;

    // send report even nothing changed? (korona only)
    protected $forceSendReport = false;

    // include only short log information in the result email (korona only)
    protected $shortLog = true;

    // recipients for the result email (korona only)
    protected $reportRecipients = '';

    // recipients for administration emails (korona only)
    protected $adminEmail = '';

    // api password
    protected $apiPassword = '';

    // secret key (different use in the API)
    protected $secretKey = '';

    // api endpoint
    protected $apiEndpoint = '';

    // api version
    protected $apiVersion = '';

    // api name for easy access in curl
    protected $apiName = '';
    
    // api url
    protected $apiUrl = '';

    // image url (does not point to the api end point)
    protected $apiImagesUrl = '';

    public function __construct()
    {

        // call the function to create the api url and set the api name
        $this->setApiData();

    }

    // do not use return type "mixed" here, since it does not exists prior php version 8
    public function get(string $name)
    {

        // init vars
        $value = 'error';

        // does a var with this name exists?
        if (isset($this->{$name})) {

            // get the value
            $value = $this->{$name};

        }

        // return the config value
        return $value;

    }

    protected function setApiData()
    {

        // shopify?
        if (get_class($this) === 'ShopifyConfig' || get_class($this) === 'ShopifyTestConfig') {

            // set the api name
            $this->apiName = 'shopify';

            // put the api url for shopify together
            $this->apiUrl = 
                'https://' . 
                $this->shopName    . '.' . 
                $this->apiEndpoint . 
                $this->apiVersion  . '/';
            
        } else {

            // set the api name
            $this->apiName = 'korona';

            // put the api url for korona together
            $this->apiUrl = 
                'https://' . 
                $this->apiEndpoint . 
                $this->apiVersion  . 
                '/accounts/' . 
                $this->accountId . '/';

        }
        
    }

}