<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @version:     1.1.0
 * @create date: 2022-01-07
 * @modify date: 2022-03-11
 * @description: Holds the configuration which is needed to use the Shopify API
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class ShopifyConfig extends Config
{

    // shop name (needed in the url to make requests to shopify, see Config class)
    protected $shopName = 'gm-top-markt-gmbh';

    // city of the shop location defined in the settings to synchronize with (can be a partial name)
    // note: is normally the same name like the organization name in the korona config, but may differ
    protected $city = 'Wiesbaden';

    // if no id is entered here, the program detects it by the city name
    protected $locationId = '66676359388';
    
    // api access token
    protected $apiPassword = '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';

    // api secret key (needed to verify webhooks: https://shopify.dev/apps/webhooks/configuration/https)
    // not yet in use
    // protected $secretKey = '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';

    // api key (not yet in use)
    // protected $apiKey = '8a9920f17a56588bebe43cd9531b5221';

    // api endpoint
    protected $apiEndpoint = 'myshopify.com/admin/api/';

    // api version (changes every 3 month)
    // do not change it, until you are a developer and have tested all functions with a newer version
    //protected $apiVersion = '2022-01';
    protected $apiVersion = '2021-04';
 
    // live url to the service (needed for links to the images, hard coded so it can be used from a local system too)
    //protected $liveUrl = 'https://simplecms.me/api';
    protected $liveUrl = 'https://greek-foods.shop/';

    // do only create products in shopify with images
    protected $needImage = false;

    // update also title and descriptions?
    // can be disabled for faster run
    protected $updateAll = true;

    // after a product was created update the stock amount too
    // note: this should be deactivated on initial run (see $forceUpdate in Korona config),
    //  since the stock amount will be updated after all products were created
    protected $updateStock = true;

    // the amount of stock items which will be reduced in shopify to make sure, people can always
    // order an article even if there is not enough in korona
    // note: no negative values are allowed here
    protected $stockBuffer = 5;

    // if your shop is not at the same timezone as korona, use the difference in hours here (plus or minus)
    protected $timezoneOffset = 0;


}