*   Trying ***********:443...
* Connected to www.greek-foods.shop (***********) port 443 (#0)
* ALPN: offers h2
* ALPN: offers http/1.1
*  CAfile: /etc/pki/tls/certs/ca-bundle.crt
*  CApath: none
* SSL connection using TLSv1.3 / TLS_AES_256_GCM_SHA384
* ALPN: server accepted h2
* Server certificate:
*  subject: CN=mail.greek-foods.shop
*  start date: Oct  1 10:25:45 2024 GMT
*  expire date: Dec 30 10:25:44 2024 GMT
*  subjectAltName: host "www.greek-foods.shop" matched cert's "www.greek-foods.shop"
*  issuer: C=US; O=Let's Encrypt; CN=R11
*  SSL certificate verify ok.
* Using HTTP2, server supports multiplexing
* Copying HTTP/2 data in stream buffer to connection buffer after upgrade: len=0
* h2h3 [:method: POST]
* h2h3 [:path: /index.php?job=cleanLogs&key=b9e1e90e2622a40f771024144b44a39d]
* h2h3 [:scheme: https]
* h2h3 [:authority: www.greek-foods.shop]
* h2h3 [accept: */*]
* h2h3 [content-type: application/json]
* Using Stream ID: 1 (easy handle 0x27922d0)
> POST /index.php?job=cleanLogs&key=b9e1e90e2622a40f771024144b44a39d HTTP/2
Host: www.greek-foods.shop
accept: */*
content-type: application/json

< HTTP/2 200 
< x-powered-by: PHP/7.4.33
< content-type: text/html; charset=UTF-8
< date: Sun, 13 Oct 2024 21:00:15 GMT
< server: LiteSpeed
< strict-transport-security: max-age=63072000; includeSubDomains
< x-frame-options: SAMEORIGIN
< x-content-type-options: nosniff
< 
* Connection #0 to host www.greek-foods.shop left intact
<!DOCTYPE html>
        <html lang="en" dir="ltr">
            <head>
                <meta charset="utf-8">
                <meta name="robots" content="noarchive,noindex">
                <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
                <link rel="shortcut icon" href="https://greek-foods.shop/favicon.png">
            </head>
            <body><div class="uk-position-center uk-flex uk-flex-middle uk-text">
                        <div class="uk-card uk-card-primary uk-card-body uk-card-hover uk-text-large uk-height-max-large" data-uk-overflow-auto><p>Deleted: /home/<USER>/public_html/logs/2024/09/29/0025.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0055.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0042.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0049.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0032.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0006.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0020.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0029.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0041.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0018.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0035.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0005.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0050.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0023.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0045.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0009.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0012.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0030.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0053.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0054.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0044.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0034.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0046.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0051.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0013.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0039.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0043.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0017.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0011.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0024.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0010.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0048.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0047.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0022.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0021.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0031.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0019.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0007.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0026.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0052.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0027.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0016.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0015.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0036.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0037.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0002.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0028.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0033.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0038.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0003.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0004.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0040.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0014.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0001.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/09/29/0008.log<br><br>Directory deleted: /home/<USER>/public_html/logs/2024/09/29<br><br></p></div></div></body>
        </html>