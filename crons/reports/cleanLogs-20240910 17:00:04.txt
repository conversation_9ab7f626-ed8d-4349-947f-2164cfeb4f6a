*   Trying ***********:443...
* Connected to www.greek-foods.shop (***********) port 443 (#0)
* ALPN: offers h2
* ALPN: offers http/1.1
*  CAfile: /etc/pki/tls/certs/ca-bundle.crt
*  CApath: none
* SSL connection using TLSv1.3 / TLS_AES_256_GCM_SHA384
* ALPN: server accepted h2
* Server certificate:
*  subject: CN=greek-foods.shop
*  start date: Aug  1 10:12:54 2024 GMT
*  expire date: Oct 30 10:12:53 2024 GMT
*  subjectAltName: host "www.greek-foods.shop" matched cert's "www.greek-foods.shop"
*  issuer: C=US; O=Let's Encrypt; CN=R10
*  SSL certificate verify ok.
* Using HTTP2, server supports multiplexing
* Copying HTTP/2 data in stream buffer to connection buffer after upgrade: len=0
* h2h3 [:method: POST]
* h2h3 [:path: /index.php?job=cleanLogs&key=b9e1e90e2622a40f771024144b44a39d]
* h2h3 [:scheme: https]
* h2h3 [:authority: www.greek-foods.shop]
* h2h3 [accept: */*]
* h2h3 [content-type: application/json]
* Using Stream ID: 1 (easy handle 0x1d5ed20)
> POST /index.php?job=cleanLogs&key=b9e1e90e2622a40f771024144b44a39d HTTP/2
Host: www.greek-foods.shop
accept: */*
content-type: application/json

< HTTP/2 200 
< x-powered-by: PHP/7.4.33
< content-type: text/html; charset=UTF-8
< date: Tue, 10 Sep 2024 21:00:05 GMT
< server: LiteSpeed
< strict-transport-security: max-age=63072000; includeSubDomains
< x-frame-options: SAMEORIGIN
< x-content-type-options: nosniff
< 
* Connection #0 to host www.greek-foods.shop left intact
<!DOCTYPE html>
        <html lang="en" dir="ltr">
            <head>
                <meta charset="utf-8">
                <meta name="robots" content="noarchive,noindex">
                <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
                <link rel="shortcut icon" href="https://greek-foods.shop/favicon.png">
            </head>
            <body><div class="uk-position-center uk-flex uk-flex-middle uk-text">
                        <div class="uk-card uk-card-primary uk-card-body uk-card-hover uk-text-large uk-height-max-large" data-uk-overflow-auto><p>Deleted: /home/<USER>/public_html/logs/2024/08/27/0025.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0055.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0042.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0049.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0032.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0006.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0020.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0029.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0041.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0018.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0035.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0005.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0050.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0023.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0045.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0009.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0012.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0030.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0053.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0054.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0044.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0034.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0046.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0051.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0013.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0039.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0043.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0017.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0011.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0024.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0010.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0048.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0047.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0022.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0021.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0031.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0019.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0007.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0026.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0052.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0027.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0016.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0015.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0036.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0037.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0002.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0028.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0033.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0038.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0003.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0004.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0040.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0014.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0001.log<br><br>Deleted: /home/<USER>/public_html/logs/2024/08/27/0008.log<br><br>Directory deleted: /home/<USER>/public_html/logs/2024/08/27<br><br></p></div></div></body>
        </html>