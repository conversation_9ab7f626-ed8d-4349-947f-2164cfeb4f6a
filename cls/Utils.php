<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.5
 * @create date: 2022-01-07
 * @modify date: 2022-01-04
 * @description: Contains all shared and public accessible methods and controls the output of the log result.
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class Utils
{

    // unsorted vars

    // app version
    public $appVersion = '2.5.6 (2022-04-01)';

    // use test configs only
    protected $koronaTest = false;
    protected $shopifyTest = false;

    // html var for output
    protected $html = '';

    // file with the last sync run
    protected $lastSyncFile = '';

    // called job
    public $job = '';

    // how this service was called (intern or via browser/external cron service)
    public $callType = '';
    
    // var for short log (see config)
    public $shortLog = '';

    // var for log caching (see config)
    public $logCache = '';
    
    
    // statistic

    // created products
    public $created = 0;

    // updated prices
    public $prices = 0;

    // updated titles/descriptions
    public $seo = 0;

    // updated stock amounts
    public $stocks = 0;

    // created collections
    public $collectionsCreated = 0;

    // updated collections
    public $collectionsUpdated = 0;

    // created stock amount bookings in korona
    public $stockAmountBookings = 0;

    // deleted images
    public $imagesDeleted = 0;

    // updated images
    public $imagesUpdated = 0;

    // created images
    public $imagesCreated = 0;

    // warnings
    public $warnings = 0;

    // errors
    public $errors = 0;

    // script run time
    public $runTime = 0;

    // curl requests to korona
    public $koronaCurls = 0;

    // curl requests to shopify
    public $shopifyCurls = 0;


    // korona

    // korona configuration
    protected $koronaConfig = null;

    // korona test configuration
    protected $koronaTestConfig = null;

    // korona shared methods
    public $koronaUtils = null;

    // korona organization id
    public $koronaOrganizationId = '';

    // korona commodity groups
    public $koronaGroups = [];

    // korona products
    public $koronaProducts = [];

    // korona stock items
    public $koronaProductStocks = [];


    // shopify

    // shopify configuration
    protected $shopifyConfig = null;

    // shopify test configuration
    protected $shopifyTestConfig = null;

    // shopify shared methods
    public $shopifyUtils = null;

    // shopify location id (= same as organization id in korona)
    public $shopifyLocationId = '';

    // shopify collections
    public $shopifyCollections = [];

    // shopify products
    public $shopifyProducts = [];

    // shopify orders
    public $shopifyOrders = [];

    // shopify inventory items
    public $shopifyInventoryItems = [];

    
    // other objects

    // curl object
    public $curl = null;

    // logging object
    public $log = null;

    // lock file object
    public $lockFile = null;


    // directories
    
    // data directory
    public $dataDir = '';

    // data groups directory
    public $dataGroupsDir = '';

    // data orders directory
    public $dataOrdersDir = '';

    // data revisions directory
    public $dataRevisionsDir = '';

    // data seo directory
    public $dataSeoDir = '';
    
    public function __construct()
    {

        // set explicitly the MEZ time zone if local time differs
        // note: this is needed for time comparisons in the ShopifyOrders class
        // date_default_timezone_set("Europe/Berlin");

        // create all shared objects
        $this->createObjects();

        // create necessary directories
        $this->createDirectories();

        // set the file for the last sync
        $this->lastSyncFile = $this->dataOrdersDir . 'lastSync.txt';

        // try to set the korona organization id from the config
        $this->koronaOrganizationId = $this->get('korona', 'organizationId');

        // no organization id configured?
        if (strlen($this->koronaOrganizationId) < 20) {

            // set the korona organization id by the organization name
            $this->koronaUtils->getOrganizationIdByName();

        }

        // try to set the shopify location id from the config
        $this->shopifyLocationId = $this->get('shopify', 'locationId');

        // no location id configured?
        if (strlen($this->shopifyLocationId) < 10) {

            // set the shopify location id by the city name
            $this->shopifyUtils->getLocationIdByCity();

        }

    }

    public function createObjects()
    {

        // create curl object
        $this->curl = new Curl($this);

        // create logging object
        $this->log = new Log($this);

        // create lock file object
        $this->lockFile = new LockFile($this);

        // create object from shopify config
        $this->shopifyConfig = new ShopifyConfig();

        // create object from shopify test config
        if ($this->shopifyTest === true) $this->shopifyTestConfig = new ShopifyTestConfig();

        // create object from shopify utils
        $this->shopifyUtils = new ShopifyUtils($this);

        // create object from korona config
        $this->koronaConfig = new KoronaConfig();

        // create object from korona utils
        $this->koronaUtils = new KoronaUtils($this);

        // create object from korona test config
        if ($this->koronaTest === true) $this->koronaTestConfig = new KoronaTestConfig();

    }

    protected function createDirectories() : void
    {

        // create log directories
        $this->log->createDirectories();

        // path to the data directory
        $this->dataDir = dirname(__DIR__) . '/data/';

        // path to the data groups directory
        $this->dataGroupsDir = $this->dataDir . 'groups/';
            
        // make sure the path exists
        @mkdir($this->dataGroupsDir, 0700, true);

        // path to the data revisions directory
        $this->dataRevisionsDir = $this->dataDir . 'revisions/';
            
        // make sure the path exists
        @mkdir($this->dataRevisionsDir, 0700, true);

        // path to the orders directory
        $this->dataOrdersDir = $this->dataDir . 'orders/';
            
        // make sure the path exists
        @mkdir($this->dataOrdersDir, 0700, true);

        // path to the data seo directory
        $this->dataSeoDir = $this->dataDir . 'seo/';
            
        // make sure the path exists
        @mkdir($this->dataSeoDir, 0700, true);

    }
    
    // do not use return type "mixed" here, since it does not exists prior php version 8
    public function get(string $api, string $name)
    {

        // get the needed config
        $config = $this->getConfig($api);

        // return the wanted value
        return $config->get($name);

    }

    public function readInput(string $type, string $name, int $maxLength=0, string $default='')
    {
       
        // get post data?
        if (strtolower($type) === 'post') {

            // get the input post value
            $value = filter_input(INPUT_POST, $name, FILTER_SANITIZE_STRING);

        } else {

            // get the input get value
            $value = filter_input(INPUT_GET, $name, FILTER_SANITIZE_STRING);

        }

        // if no value is given, use the default value
        if ($value === 'undefined' || $value === null) $value = $default;

        // restrict value length?
        if ($maxLength > 0) $value = mb_substr($value, 0, $maxLength);

        // if no value is present use the default value
        if (mb_strlen($value) === 0) $value = $default;

        // decode html special characters
		$value = htmlspecialchars_decode($value);

        // return the value
        return trim($value);

    }

    public function getIdFromGid(string $gid) : string
    {

        // gid://shopify/Order/4225066631356

        // init vars
        $id = $gid;

        // get the last slash
        $slash = strrpos($gid, '/');

        // found?
        if ($slash > -1) {

            // get the id only
            $id = substr($gid, $slash + 1);

        }

        // return the id
        return $id;

    }
    
    public function curlExec(string $api, string $method, string $body, string $resource)
    {

        // get the needed config
        $config = $this->getConfig($api);

        // call the curl class
        $result = $this->curl->curlExec($config, $method, $body, $resource);

        // return the result
        return $result;

    }

    protected function getConfig(string $api) : object
    {

        // use test configs only?
        if ($this->koronaTest === true && $api === 'korona') $api = 'koronaTest';
        if ($this->shopifyTest === true && $api === 'shopify') $api = 'shopifyTest';

        // get the needed config
        switch ($api) {

            case 'shopify':

                // use shopify config
                $config = $this->shopifyConfig;

                break;

            case 'shopifyTest':

                // use shopify test config
                $config = $this->shopifyTestConfig;

                break;

            case 'korona':

                // use korona config
                $config = $this->koronaConfig;

                break;

            case 'koronaTest':

                // use korona test config
                $config = $this->koronaTestConfig;

                break;
            
            default:

                // show error and die
                $this->logError('error', 'Invalid API: ' . $api, null, true);
                
                break;

        }

        // return the config
        return $config;

    }
    
    public function logCurl(string $text, $data=null) : void
    {

        // log the error
        $this->logData('curl', $text, $data, false);
        
	}

    public function logInfo(string $text, $data=null, bool $shortLog=false) : void
    {
               
        // log the info
        $this->logData('info', $text, $data, false);

        // extend the short log?
        if ($shortLog === true) $this->shortLog .= $text . "\n\n";

	}
    
    // public function logError(string $json) : void
    public function logError(string $text, $data=null, $die=false) : void
    {

        // log the error
        $this->logData('error', $text, $data, $die);
        
	}

    public function logData(string $type, string $text, $data=null, $die=false) : void
    {

        // call the log function
        $this->log->logData($type, $text, $data, $die);

    }

    public function outputResult($data=null, bool $error=false) : void
    {

        // if data is given, add it to the result
        if ($data) $this->addData($data);

        // add log data
        if ($error === false) $this->html .= $this->log->addLogData();

        // format the html
        $this->html = str_replace("\n", '<br>', $this->html);

        // put html page together
        $html = '<!DOCTYPE html>
        <html lang="en" dir="ltr">
            <head>
                <meta charset="utf-8">
                <meta name="robots" content="noarchive,noindex">
                <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
                <link rel="shortcut icon" href="https://greek-foods.shop/favicon.png">
            </head>
            <body>';

        // debug result?
        if (strpos($this->html, '=>') > -1) {

            // open pre tag
            $html .= '<pre style="color:black;overflow:auto">';
            
            // add the result
            $html .= $this->html;

            // close pre tag
            $html .= '</pre>';

        } else {

            // open card
            $html .= '<div class="uk-position-center uk-flex uk-flex-middle uk-text">
                        <div class="uk-card uk-card-primary uk-card-body uk-card-hover uk-text-large uk-height-max-large" data-uk-overflow-auto>';
            
            // add string result
            $html .= $this->html;

             // close the card
            $html .= '</div></div>';

        }        

        // close the html
        $html .= '</body>
        </html>';

        // remove single commas created by var_export function
        $html = str_replace("'", '', $html);

        // output the html
        die($html);

    }
    
    protected function addData($data)
    {
        
        // put the data into a var
        $debug = var_export($data, true);

        // debug data?
        if (strpos($debug, '=>') > -1) {
        
            // add the debug data to the html var
            $this->html .= '<p>' . $debug . '</p>';

        } elseif (empty($data)) {

            // add note to the html var
            $this->html .= '<p>No result given</p>';

        } elseif (gettype($data) === 'object') {

            // add the object data to the html var
            $this->html .= '<p>' . var_export($data, true) . '</p>';
            
        } else {

            // add the normal data to the html var
            $this->html .= '<p>' . $data . '</p>';

        }

    }

    public function isLocal() : bool
    {

        // get the server ip address
        $ip = $this->getServerIp();

        // local address?
        return substr($ip, 0, 3) === '127';

    }

    public function getServerIp() : string
    {

        // init vars
        $ip = '';

        // do not use this, since it may return from cron jobs the local ip address,
        // depending on the server configuration:
        
        // // $_SERVER has only values if the call comes from a browser/curl request
        // if (isset($_SERVER['SERVER_ADDR'])) {

        //     // use ip from server var
        //     $ip = $_SERVER['SERVER_ADDR'];

        // } else {

            // get the hostname
            $host= gethostname();

            // get the ip of the hostname
            $ip = gethostbyname($host);

        // }

        // return the ip
        return $ip;

    }

    public function getRevision(string $name) : int
    {
 
        // init vars
        $revision = 0;

        // when forcing an update do not use a revision number to get all items, not only the changed
        if ($this->get('korona', 'forceUpdate') === true) return 0;
        
        // get file to write the number
        $file = $this->getRevisionFile($name);

        // file exists?
        if (file_exists($file)) {

            // read the revision number
            $revision = file_get_contents($file);

            // problem?
            if ($revision === false) {

                // set it to 0 to get all items
                $revision = 0;

            }

        }       

        // return the maximum revision
        return $revision;

    }

    public function setRevision(string $name, int $value) : bool
    {
 
        // get file to write the number
        $file = $this->getRevisionFile($name);

        // write it into the file
        return $this->saveFile($file, $value);
        
    }

    protected function getRevisionFile(string $name) : string
    {

        // return the revision file
        return $this->dataRevisionsDir . $name . '.txt';

    }

    public function removeDoubleSpaces(string &$text) : void
    {

        // double spaces found?
        while (strpos($text, '  ') > -1) {

            // remove them
            $text = str_replace('  ', ' ', $text);

        }

    }

    public function getLastSyncTime() : string
    {

        // file not exists?
        if ( ! file_exists($this->lastSyncFile)) {

            // set the last run date 7 days ago
            $date = date('Y-m-d', strtotime('-7 days'));

        } else {

            // read the last modification time
            // $time = filemtime($this->lastSyncFile);
            // note: do not do that, because if the finished time can not set via touch
            // this will result in data mess, since the user have to "touch" the file
            // manually on the server and you do not know when he will do this.

            // read the date from the file content
            $date = trim(file_get_contents($this->lastSyncFile));

            // failed?
            if ($date === false) {

                // die with an error
                $this->logError('Could not read last sync time from file ' . $this->lastSyncFile . ' (permissions problem?)', null, true);

            }

            // get the date
            $date = $this->getSyncDate($date);
                        
        }

        // return the date
        return $date;

    }

    protected function getSyncDate(string $date) : string
    {

        // try to create the date from the string
        try {

            // read the timezone offset from the shopify config
            $timezoneOffset = $this->get('shopify', 'timezoneOffset');

            // create a date time object from the date
            $modifiedDate = new DateTime($date);

            // modify it with the timezone offset
            $modifiedDate->modify($timezoneOffset . ' hour');

            // change the date (format: 2022-02-02T05:20:12Z)
            // note: compare only the date not the time to make sure orders will be processed
            // even if a temporarily error happened
            // $date = $modifiedDate->format('Y-m-d') . 'T' . $modifiedDate->format('H:i:s') . 'Z';
            $date = $modifiedDate->format('Y-m-d') . 'T00:00:00Z';

            // return the date
            return $date;

        } catch (Exception $error) {

            // log the error and die
            $this->logError('Error getting last sync date: ' . $error->getMessage(), null, true);

        }

    }

    public function setLastSyncTime() : void
    {

        // build the date from the last sync time
        $date = date('Y-m-d') . 'T' . date('H:i:s') . 'Z';

        // save last run date
        if ($this->saveFile($this->lastSyncFile, $date) === false) {

            // create the message
            $message = 'Could not save last sync time to file ' . $this->lastSyncFile . ' (permissions problem?)' . "\n";
            $message .= 'Please open the file manually on the server and put the following data in it and save it: ' . $date;

            // die with an error
            $this->logError($message, null, true);

        }

        // do not do this (see getLastSyncTime)
        // @touch($this->lastSyncFile);

    }

    public function saveHash(string $imageFile, string $sku, int $position, string $target='images') : void
    {

        // get the hash value of the image
        $hash = md5_file($imageFile);

        // get the target directory
        $directory = dirname(__DIR__) . '/' . $target . '/' . $sku . '/';

        // set the hash target file
        $hashFile = $directory . $sku . '-' . $position . '-hash.txt';
        
        // make sure path exists
        @mkdir(dirname($hashFile), 0700, true);

        // save it
        $this->saveFile($hashFile, $hash);
        
    }

    public function checkJson() : bool
    {

        // no error ocurred?
        if (json_last_error() === JSON_ERROR_NONE) return true;

        // increase error counter
        $this->errors++;

        // log the error
        $this->logInfo('Error: ' . json_last_error_msg());

        // return error
        return false;

    }
 
    public function saveFile(string $file, string $content, bool $append=false) : bool
    {

        // append content to the file?
        if ($append === true) {

            // save the file and append the content if the file already exists
            $bytes = @file_put_contents($file, $content, FILE_APPEND);
            
        } else {

            // save the file and overwrite an existing one
            $bytes = @file_put_contents($file, $content);

        }

        // failed?
        if ($bytes === false || $bytes === 0) return false;

        // image file?
        if (strpos($file, '/images/') !== false && strpos($file, '.txt') === false) {

            // image files must be public accessible
            @chmod($file, 0644);

        } else {

            // restrict access to the webserver user only
            @chmod($file, 0600);

        }        

        // file saved
        return true;

    }

}