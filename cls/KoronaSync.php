<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.0
 * @create date: 2022-01-31
 * @modify date: 2022-03-04
 * @description: Controls the synchronization of the commodity groups, prices ans stock amounts from Korona to Shopify
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class KoronaSync
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function syncProducts() : void
    {
        
        // to create a product with korona groups the ids of the corresponding collection is needed
        $this->utils->shopifyUtils->updateShopifyCollections();

        // fetch all products in the online shop
        $this->utils->shopifyUtils->getShopifyProducts();

        // log info
        $this->utils->logInfo('Fetched Shopify products: ' . count($this->utils->shopifyProducts));

        // first update the product price, because this method will create missing products in shopify
        $this->utils->koronaUtils->updateProducts();

        // created new products in shopify?
        if ($this->utils->created > 0) {

            // reset the shopify products array
            unset($this->utils->shopifyProducts);

            // reinitialize it
            $this->utils->shopifyProducts = [];
            
            // fetch all products in the online shop again since updateProducts may created new products
            $this->utils->shopifyUtils->getShopifyProducts();

            // log info
            $this->utils->logInfo('Fetched Shopify products: ' . count($this->utils->shopifyProducts));
            
        }
        
        // update the stock amount
        $this->utils->koronaUtils->updateStocks();

    }

}