<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.2.1
 * @create date: 2022-01-31
 * @modify date: 2022-03-18
 * @description: Provides shared methods for the Korona api
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class KoronaUtils
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function syncProducts() : void
    {

        // korona api is needed for this function
        $class = new KoronaSync($this->utils);

        // call the function
        $class->syncProducts();

    }

    public function updateProducts() : void
    {

        // korona api is needed for this function
        $class = new KoronaProductUpdateProducts($this->utils);

        // call the function
        $class->updateProducts();

    }

    public function setStockAmount(object &$koronaProduct) : void
    {

        // korona api is needed for this function
        $class = new KoronaProductUpdateStocks($this->utils);

        // call the function
        $class->setStockAmount($koronaProduct);
        
        
       

    }

    public function updateStocks(bool $force=false) : void
    {

        // korona api is needed for this function
        $class = new KoronaProductUpdateStocks($this->utils);

        // call the function
        $class->updateStocks($force);

    }

    public function updateImages() : void
    {

        // update images is needed for this function
        $class = new KoronaProductUpdateImages($this->utils);

        // call the function
        $class->updateImages();

    }

    public function addStockAdjustment(object $order, string $type) : void
    {

        // korona api is needed for this function
        $class = new KoronaStockAdjustment($this->utils);

        // call the function
        $class->addStockAdjustment($order, $type);

    }

    public function setSeo(object &$koronaProduct) : void
    {

        // use seo object
        $class = new Seo($this->utils);

        // make the product name more human readable (before seo methods)
        $class->humanizeProductName($koronaProduct);

        // set the description text (before setSeoDescription, since it will be used there)
        $class->setDescription($koronaProduct);

        // set the seo title
        $class->setSeoTitle($koronaProduct);

        // set the seo description
        $class->setSeoDescription($koronaProduct);

        // make it html conform
        $class->convert2Html($koronaProduct);

        // set the product handle from the seo title since it can be different as the product title
        $koronaProduct->handle = $this->getSlug($koronaProduct->seoTitle);
        
        
        

    }

    public function setSingleDescription(string $sku) : void
    {

        // use seo object
        $class = new Seo($this->utils);

        // set the description for a single sku number
        $class->setSingleDescription($sku);

    }

    public function getSlug(string $text) : string
    {
        
        // if no slug object is created, create a new one
        $class = new Slug($this->utils);

        // create a standard object as param for the slug method
        $object = new \stdClass();

        // add the needed data to the object
        $object->text = $text;
        // $object->maxLength = 0;

        // get a seo friendly slug for the image name
        $slug = $class->getSlug($object);

        // return the slug
        return $slug;

    }

    // using the maximum size of 1000 per page results in errors, so we use only 250
    // note: increasing the size may need to increase the timeout in the curl class too
    // note: 1 page with size 250 articles has about 500 kbyte data to transfer (2022-03-20)
    public function getKoronaProducts(int $revision=0, int $page=1, int $size=250) : void
    {

        // log info
        $this->utils->logInfo('Fetching Korona articles page ' . $page . '...');

        // put request together
        // note: do not use a revision number for data sync, since we need always all products to update the stock amount
        $get = 'products/?includeDeleted=false&sort=number&size=' . $size . '&tag=Onlineshop&page=' . $page;

        // revision number given? (only for updating images)
        if ($revision > 0) $get .= '&revision=' . $revision;

        // get the products for the online shop (default return size is 1000 items)
        $result = $this->utils->curlExec('korona', 'GET', '', $get);
        
      /*  
         $date_time = strftime("%Y%m%d %H:%M:%S",time());
                    $fpa = fopen(dirname(__FILE__).'/temp/koronaUtils_getKoronaProducts - '.$date_time.'.txt', 'w'); 
                    fwrite($fpa, 'Curl Result'."\r\n");
                    fwrite($fpa, json_encode($result)."\r\n");
                    fclose($fpa);
*/
        // revision given?
        if ($revision > 0) {

            // no items found?
            if ($result === '') {

                // log info
                $this->utils->logInfo('No changed Korona articles found with revision < ' . $revision);

                // nothing more to do
                return;

            }

        }
// $this->utils->logInfo('Found articles: ', $result, true);
        // problem?
        if ( ! isset($result->results)) {

            // log number of not found items, since the result var still contains them, but may be cut off
            $this->utils->logInfo('Found articles: ' . count($result->results));

            $this->utils->logInfo('Found articles: ', $result, true);

            // log error and die
            // note: changes here must be made in LockFile class too (isTempError)
            $this->utils->logError('Can not read Korona articles', $result, true);
            
        }

        // problem?
        if (count($result->results) === 0) {

            // log error and die
            $this->utils->logError('No articles with the tag "Onlineshop" found', $result, true);

        }
        
        // log info
        $this->utils->logInfo('Merging Korona articles...');

        // merge the result with the products array to avoid getting another array for each page in the products array
        $this->utils->koronaProducts = array_merge($this->utils->koronaProducts, $result->results);

        // log info
        $this->utils->logInfo('...success.');

        // another page given and fetch all items?
        if ($result->currentPage < $result->pagesTotal && $size === 250) {

            // increase the page counter
            $page++;

            // log info
            $this->utils->logInfo('Fetching Korona articles next page ' . $page . '...');

            // fetch the next page (< 25 prevents endless loop)
            if ($page < 25) $this->getKoronaProducts($revision, $page, $size);

        }

    }

    public function getOrganizationIdByName(string $organizationName='') : void
    {
        
        // init vars
        $organizationId = '';

        // no name given?
        if ($organizationName === '') {

            // get it from the configuration file
            // get the (partial) name of the organization
            $organizationName = $this->utils->get('korona', 'organizationName');

            // problem?
            if ($organizationName === '') {

                // log error and die
                $this->utils->logError('Can not read Korona organization name (please check your configuration)', null, true);

            }

        }

        // execute the curl request
        $result = $this->utils->curlExec('korona', 'GET', '', 'organizationalUnits');
        
        // problem?
        if ( ! isset($result->results) || count($result->results) === 0) {

            // log error and die
            $this->utils->logError('Can not read Korona organization units', $result, true);

        }
        
        // loop through the results
        foreach ($result->results as $result) {

            // wanted organization found?
            if (strpos(strtolower($result->name), strtolower($organizationName)) > -1) {

                // no organization id yet found?
                if ($organizationId === '') {

                    // set the id
                    $organizationId = $result->id;

                } else {

                    // log error and die
                    $this->utils->logError('Can not find a unique organization id for the name "' . $organizationName . '". Please check the Korona configuration and make sure the organization name is unique.', $result, true);

                }               

            }

        }

        // not found?
        if ($organizationId === '') {

            // log error and die
            $this->utils->logError('Organization Id for organization "' . $organizationName . '" not found.', $result, true);

        } else {

            // log info
            $this->utils->logInfo('Organization Id for organization "' . $organizationName . '" is: ' . $organizationId);

        }
        
        // set the id
        $this->utils->koronaOrganizationId = $organizationId;

    }

    public function getArticleIdBySku(string $sku) : string
    {

        // init vars
        $articleId = '';

        // get the article index
        $index = array_search($sku, array_column($this->utils->koronaProducts, 'number'));

        // get the article object
        $koronaProduct = $this->utils->koronaProducts[$index];
        
      

        // found?
        if (isset($koronaProduct) && isset($koronaProduct->id)) {

            // get the id
            $articleId = $koronaProduct->id;

        } else {

            // log error and die
            $this->utils->logError('Article with sku ' . $sku . ' not found in the korona articles', null, true);

        }
        // $this->utils->outputResult($koronaProduct);

        // return the id
        return $articleId;

    }

    public function deleteArticles() : void
    {

        // get all articles
        $this->getKoronaProducts();

        // loop through the articles
        foreach ($this->utils->koronaProducts as $product) {

            // $this->utils->outputResult($product);

            // delete it
            $result = $this->utils->curlExec('koronaTest', 'DELETE', '', 'products/' . $product->id);

            // $this->utils->outputResult($result);

        }

    }

}