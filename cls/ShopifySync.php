<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.0
 * @create date: 2022-01-07
 * @modify date: 2022-03-07
 * @description: Controls the orders synchronization process
 */

// no direct access
defined('GM_EXEC') or die('Access denied');

class ShopifySync
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function syncOrders() : void
    {

        // get the last orders
        $this->utils->shopifyUtils->getOrders();

// var_export($this->utils->shopifyOrders);die('showOrders');

        // put them into a local var
        $orders = $this->utils->shopifyOrders;

        // get the last sync date/time
        $date = $this->utils->getLastSyncTime();

        // create a date/time object from the string date
        $date = new DateTime($date);

        // get the numbers of the orders for the log
        $info = $this->getOrderInfo($orders);

        // log info
        $this->utils->logInfo('Changed orders since ' . $date->format('d.m. G:i:s') . ' in Shopify: ' . count($orders) . $info, null, true);

        // nothing found?
        if (count($orders) === 0) {
            
            // nothing to do
            return;

        }
            
// $this->utils->outputResult($orders);

        // loop through the orders
        foreach ($orders as $order) {

            // var_export($this->utils->shopifyOrders);die('huhuhu');

            // fulfillments given?
            if (count($order->fulfillments) > 0 ) {

                // process the fulfillments
                $this->utils->koronaUtils->addStockAdjustment($order, 'fulfillments');

            }

            // refunds given?
            if (count($order->refunds) > 0) {

                // process the refunds
                $this->utils->koronaUtils->addStockAdjustment($order, 'refunds');

            }
            
        }

        // set last run time
        $this->utils->setLastSyncTime();

    }

    protected function getOrderInfo(array $orders) : string
    {

        // init vars
        $info = '';

        // no new orders?
        if (count($orders) === 0) return $info;

        // loop through the orders
        foreach ($orders as $order) {

            // extend the info
            $info .= $order->name . ' / ';

        }

        // remove last slash
        $info = rtrim($info, ' / ');

        // put the info into brackets
        $info = ' (' . $info . ')';

        // return the info
        return $info;

    }

}