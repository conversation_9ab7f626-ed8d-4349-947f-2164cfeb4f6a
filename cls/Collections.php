<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.0
 * @create date: 2022-01-07
 * @modify date: 2022-03-02
 * @description: Mirrors the commodity groups from Korona to the categories in Shopify
 *               To detect changes, all groups are cached in files after a category was created in Shopify
 *               Note: Korona has nested groups while Shopify has not
 *               Note: To be able in the ShopifyProductCreate class assign products to different tax classes
 *                     we have to create them here, regardless if they exists in Korona.
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class Collections
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function updateShopifyCollections() : void
    {

        // add log info
        $this->utils->logInfo('Updating/creating collections in Shopify...');

        // get the commodity groups from korona
        $this->getCommodityGroups();
        
        // get the collections from shopify
        $this->getCollections();

        // add log info
        $this->utils->logInfo('Update existing collections');

        // update existing collections
        $this->updateCollections();
        
        // add log info
        $this->utils->logInfo('Create new collections');

        // create missing collections and/or update existing
        $this->createCollections();

        // check/create special tax collections
        $this->checkTaxCollections();

        // get the (created) collections from shopify
        $this->getCollections();

        // log info
        $this->utils->logInfo('Collections created/updated.');

    }

    protected function updateCollections() : void
    {

        // loop through the commodity groups from the back to have them in the same order as in korona
        foreach ($this->utils->koronaGroups as $group) {
            
            // get the file path to the group
            $file = $this->utils->dataGroupsDir . $group->id . '.txt';

            // not found?
            if ( ! file_exists($file)) {

                // add log info
                $this->utils->logInfo('File not found: ' . $file);

                // continue
                continue;

            }

            // read the group info
            $info = file_get_contents($file);

            // group info is json encoded
            $info = json_decode($info);

            // get the index in the array
            $index = array_search($info->collectionId, array_column($this->utils->shopifyCollections, 'id'));

            // get the collection of the array
            $collection = $this->utils->shopifyCollections[$index];

            // problem?
            if (empty($collection)) {

                // log info
                $this->utils->logInfo('Collection with index . ' . $index . ' not found in array.');

                // skip item
                continue;

            }
            
            // group name changed?
            // note: check on Shopify too, since if a user changed a category name in shopify
            // it shall be overwritten by korona to have the same groups/collections/categories
            if ($group->name != $info->groupName || $collection->title != $group->name) {
                
                // update the collection
                $this->updateCollection($info->collectionId, $group->name);

                // update the data on disk (after the update because it mail fail)
                $this->saveGroupInfo($info->collectionId, $group);

            }
            
        }
        
    }

    protected function updateCollection(string $id, string $name) : void
    {

        // set the body for the request
        $body = '{"custom_collection":{"custom_collection_id":' . $id . ',"title":"' . $name . '"}}';

        // add the custom collection
        $result = $this->utils->curlExec('shopify', 'PUT', $body, 'custom_collections/' . $id . '.json');
        
        // problem?
        if ( ! isset($result->custom_collection->title) || $result->custom_collection->title !== $name) {

            // log error and die
            $this->utils->logError('Can not update custom collection ' . $name, $result, true);

        } else {

            // update collection in collections array
            if ($this->updateCollectionsArray($id, $name) !== true) {

                // log error and die
                $this->utils->logError('Can not update collection ' . $name . ' in custom collections array', null, true);

            }

            // increase counter for the updated groups
            $this->utils->collectionsUpdated++;

            // log info
            $this->utils->logInfo('Collection updated: ' . $name, null, true);

        }
        
    }

    protected function updateCollectionsArray(string $id, string $name) : bool
    {

        // init vars
        $found = false;

        // get the number of collections
        $collections = count($this->utils->shopifyCollections);

        // loop through the array
        for ($index = 0; $index < $collections; $index++) {
        
            // get the collection
            $collection = $this->utils->shopifyCollections[$index];

            // found the collection?
            if ($collection->id == $id) {
        
                // change the title
                $this->utils->shopifyCollections[$index]->title = $name;
                
                // change var
                $found = true;

                // leave the loop
                break;

            }

        }

        // return the result
        return $found;

    }

    protected function createCollections() : void
    {

        // init vars
        $exists = false;
        
        // loop through the commodity groups
        foreach ($this->utils->koronaGroups as $group) {
            
            // reset var
            $exists = false;

            // loop through the collections
            foreach ($this->utils->shopifyCollections as $collection) {
                
                // group exists?
                if ($collection->title == $group->name) $exists = true;

            }

            // group not exists in collection?
            if ($exists === false) {
                
                // create collection with the group name
                $id = $this->createCollection($group->name);

                // save the data to disk
                $this->saveGroupInfo($id, $group);

            }
            
        }
        
    }

    protected function saveGroupInfo(int $id, object $group) : void
    {

        // get the file path
        $file = $this->utils->dataGroupsDir . $group->id . '.txt';

        // set the data
        $data = array(
            "groupId" => $group->id,
            "groupNumber" => $group->number,
            "groupName" => $group->name,
            "collectionId" => $id,
            "collectionTitle" => $group->name
        );

        // save the data to a file to watch it for renamed groups
        if ($this->utils->saveFile($file, json_encode($data, JSON_PRETTY_PRINT)) !== true) {

            // log error and die
            $this->utils->logError('can not save group data to <b>' . $file . '</b>', null, true);

        }

    }

    protected function checkTaxCollections() : void
    {

        // note: changes here must be made in ShopifyProductCreate class too (addTaxCollection)
        
        // init vars
        $freeExists = false; $halfExists = false; $fullExists = false;

        // loop through the collections in shopify
        foreach ($this->utils->shopifyCollections as $collection) {

            // special group exists?
            if ($collection->title == 'Steuerfrei') $freeExists = true;
            if ($collection->title == 'Vermindert') $halfExists = true;
            if ($collection->title == 'Voller Satz') $fullExists = true;

        }

        // if collections does not exists, create them
        if ($freeExists === false) $this->createCollection('Steuerfrei');
        if ($halfExists === false) $this->createCollection('Vermindert');
        if ($fullExists === false) $this->createCollection('Voller Satz');

    }

    protected function createCollection(string $name) : int
    {

        // set the body for the request
        $body = '{"custom_collection":{"title":"' . $name . '", "sort_order": "alpha-asc"}}';

        // add the custom collection
        $result = $this->utils->curlExec('shopify', 'POST', $body, 'custom_collections.json');

        // problem?
        if ( ! isset($result->custom_collection->id)) {

            // log error and die
            $this->utils->logError('Can not create custom collection ' . $name, $result, true);

        }

        // increase counter for the created groups
        $this->utils->collectionsCreated++;
        
        // log info
        $this->utils->logInfo('Collection created: ' . $name, null, true);
 
        // get the id
        $id = $result->custom_collection->id;
        
        // return the id
        return $id;
        
    }

    protected function getCommodityGroups() : void
    {

        // fetch all korona groups
        $result = $this->utils->curlExec('korona', 'GET', '', 'commodityGroups/?includeDeleted=false&size=250');
        // $this->utils->outputResult($result->results);
        // problem?
        if ( ! isset($result->results) || count($result->results) === 0) {

            // log error and die
            $this->utils->logError('Can not read commodity groups', $result, true);

        }

        // loop through the groups
        foreach ($result->results as $group) {

            // add group to the array
            $this->utils->koronaGroups[] = $group;

        }

        // sort it by number to have the same order in shopify as in korona
        // note: sort order in shopify is by alphabet or dates
        // usort($this->utils->koronaGroups,function ($a, $b) {
        //     return $a->number < $b->number;
        // });
        
        // log info
        $this->utils->logInfo('Commodity groups found in Korona: ' . count($this->utils->koronaGroups));
        // $this->utils->logInfo('Commodity groups in Korona: ', $this->utils->koronaGroups);
                
    }

    protected function getCollections() : void
    {

        // get current collections
        // todo: use pagination for unlimited collections
        $result = $this->utils->curlExec('shopify', 'GET', '', 'custom_collections.json?limit=250');

        // problem?
        if ( ! isset($result->custom_collections)) {

            // log error and die
            $this->utils->logError('Can not read custom collections', $result, true);

        }

        // update collections var
        $this->utils->shopifyCollections = $result->custom_collections;

        // log info
        $this->utils->logInfo('Collections found in Shopify: ' . count($this->utils->shopifyCollections));
        // $this->utils->logInfo('Collections in Shopify: ', $this->utils->shopifyCollections);

    }
    
}