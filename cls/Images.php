<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.2.0
 * @create date: 2022-01-07
 * @modify date: 2022-03-20
 * @description: Saves images from Korona locally with seo friendly file names and adds them with alt attributes
 *               to the korona product object.
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class Images
{

    // utils object
    protected $utils = null;

    // slug object
    protected $slug = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    //public function setImages(KoronaProduct &$koronaProduct): void
    public function setImages(object &$koronaProduct) : void
    {

        // init vars
        $images = []; $position = 1;

        // get the sku
        $sku = $koronaProduct->number;
        
        // $this->utils->outputResult($koronaProduct);

        // is a uploaded image given?
        // a lot of items have the same image twice, but in different formats and sizes
        // if (isset($koronaProduct->image->id)) {            
            
        //     // log info
        //     $this->utils->logInfo('Getting main image for Korona product ' . $koronaProduct->name . '...');
            
        //     // get the product image
        //     $data = $this->utils->curlExec('korona', 'GET', '', 'images/' . $koronaProduct->image->id);

        //     // image less than 1 kb?
        //     if (strlen($data) < 1024) {

        //         // log error
        //         $this->utils->logInfo('...failed: no or too less data returned for the image with id ' . $koronaProduct->image->id . ' (data length: ' . strlen($data) . ' bytes)');

        //     } else {

        //         // try to download the image data
        //         $image = $this->saveImage($koronaProduct, $data, $position);
                        
        //         // successful?
        //         if (count($image) === 4) {
                    
        //             // add it to the product images
        //             $this->addImage($koronaProduct, $image);

        //         } else {

        //             // log info
        //             $this->utils->logInfo('...failed.');

        //         }
                
        //     }
            
        // }

        // media urls given?
        if (isset($koronaProduct->mediaUrls)) {

            // loop through the urls
            foreach ($koronaProduct->mediaUrls as $mediaUrl) {
                
            //$headers = get_headers($mediaUrl->url, 1);
            //if (strpos($headers['Content-Type'], 'image/') !== false) {
            
            if ( file_exists($mediaUrl->url) )               {
                // log info
                $this->utils->logInfo('Getting image from url ' . $mediaUrl->url . ' for Korona product ' . $koronaProduct->name . '...');
                
                // skip non image urls
                if ($this->isImage($mediaUrl->url) === false) {
                 
                    // log info
                    $this->utils->logInfo('...not a valid image (png, jpg or gif)');

                    // skip image
                    continue;

                }

                // set data file in data images directory
                $dataFile = dirname(__DIR__) . '/data/images/' . $sku . '/' . $sku . '-1-hash.txt';

                // skip already created data files to not download the image again
                // important: this must only be used for testing and/or initial update!
                // if (file_exists($dataFile)) continue;

                // try to download the image data
                $image = $this->downloadImage($koronaProduct, $mediaUrl->url, $position);
                
                // successful?
                if (count($image) === 4) {
                    
                    // add it to the product images
                    $this->addImage($koronaProduct, $image);

                } else {

                    // increase warning counter
                    $this->utils->warnings++;

                    // log info
                  
                    $this->utils->logInfo('...failed downloading image from ' . $mediaUrl . ' for article ' . $koronaProduct->number . ' (' . $koronaProduct->name . ')', null, true);
                    

                }
          
            //added    
            }//if
            else
            {
                    // try to download the image data
                    $image = $this->downloadImage($koronaProduct, $mediaUrl->url, $position);
                
                    // successful?
                    if (count($image) === 4) {
                    
                    // add it to the product images
                    $this->addImage($koronaProduct, $image);
                    
                    $this->utils->logInfo('Add it to product images   '.$mediaUrl->url . ' for Korona product ' . $koronaProduct->name . '...');

                    }
                
                    // increase warning counter
                    // $this->utils->warnings++;
                    else
                    {
                    $this->utils->logInfo('Does not exist    '.$mediaUrl->url . ' for Korona product ' . $koronaProduct->name . '...');
                    }
            }//if
            //added
           
            }

        }
        // $this->utils->outputResult($koronaProduct->images);
    }

    protected function isImage(string $url) : bool
    {

        // init vars
        $ext = '';

        // work with lower case url
        $url = strtolower($url);

        // define valid image extensions (https://shopify.dev/api/admin-rest/2022-01/resources/product-image#top)
        $extensions = ['.jpeg', '.jpg', '.png', '.gif'];

        // get the dot from the back
        $dot = strrpos($url, '.');

        // if found, get the extension
        if ($dot) $ext = substr($url, $dot);

        // valid extension found?
         return in_array($ext, $extensions);

    }

    protected function addImage(object &$koronaProduct, array $image) : void
    {

        // get image data
        $width    = $image[0];
        $height   = $image[1];
        $src      = $image[2];
        $position = $image[3];

        // put alt description together
        $alt = $koronaProduct->name . ' (' . $koronaProduct->commodityGroup->name . ') - Bild ' . $position;

        // replace quotes
        $alt = str_replace('"', '&quot;', $alt);

        // $images[] = '{"attachment":"' . base64_encode($data) . '\n"}';
        $koronaProduct->shopifyImages[] = '{"src":"' . $src . '", "position":' . $position . ', "width": ' . $width . ', "height": ' . $height . ', "alt":"' . $alt . '"}';

        // log info
        $this->utils->logInfo('...done.');

    }

    protected function saveImage(object &$koronaProduct, string $data, int &$position) : array
    {

        // init vars
        $image = [];

        // try to get info about the image
        $info = @getimagesizefromstring($data);
        
        // problem?
        if ( ! $info) return $image;

        // get the size
        $image[0] = $info[0];
        $image[1] = $info[1];
        
        // get the mime type
        $mimeType = $info['mime'];

        // get the file extension
        $ext = str_replace('image/', '', $mimeType);

        // set the relative path
        $path = '/images/' . $koronaProduct->number . '/';

        // set the image directory
        $imgDir = dirname(__DIR__) . $path;

        // make sure the path exists (must be public accessible to download images in shopify)
        @mkdir($imgDir, 0755, true);

        // get the text for the slug
        $text = strlen($koronaProduct->name) < 35 ? $koronaProduct->name . '-' . $koronaProduct->commodityGroup->name : $koronaProduct->name;
        
        // get a seo friendly slug for the image name
        $slug = $this->utils->koronaUtils->getSlug($text);

        // put the file name together
        $file = $imgDir . $slug . '-' . $position . '.' . $ext;

        // same image of the same size already exists?
        // if ($this->checkImage($imgDir, $data)) {

        //     // log info
        //     $this->utils->logInfo('Image for sku '. $koronaProduct->number . ' skipped (image with the exactly same size already exists)');

        //     // return empty array
        //     return [];

        // }

        // save the image
        if ($this->utils->saveFile($file, $data) !== true) {

            // log info
            $this->utils->logInfo('Could not save image for article with sku ' . $koronaProduct->number . ' in ' . $file);

            // return empty array
            return [];

        }

        // save the hash value of the image in the image dir to be able to check if an image was changed
        $this->utils->saveHash($file, $koronaProduct->number, $position, 'images');

        // get the live url
        $liveUrl = $this->utils->get('shopify', 'liveUrl');

        // remove ending slash
        $liveUrl = rtrim($liveUrl, '/');

        // put the downloadable url together
        $image[2] = $liveUrl . $path . basename($file);

        // save the position into the image array
        $image[3] = $position;

        // increase the position counter
        $position++;

        // image saved
        return $image;

    }

    protected function checkImage(string $imgDir, string $data) : bool
    {

        // init vars
        $check = false;

        // get all files in the directory
        $files = glob($imgDir . '*');

        // loop through the files
        foreach ($files as $file) {

            // get the filesize
            $filesize = filesize($file);

            // same size?
            if ($filesize == strlen($data)) $check = true;

        }

        // return the result
        return $check;

    }

    protected function downloadImage(object &$koronaProduct, string $url, int &$position) : array
    {

        // init vars
        $image = [];

        // init curl function
        $curl = curl_init();
  
        // set options
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_URL, $url);
    
        // execute the request and get the data
        $data = curl_exec($curl);

        // close curl
        curl_close($curl);
        
        // image data found?
        if (strlen($data) > 500) {
            
            // save the image to disk
            $image = $this->saveImage($koronaProduct, $data, $position);

        }

        // return the result
        return $image;
        
    }

}