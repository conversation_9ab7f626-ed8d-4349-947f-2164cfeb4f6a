<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.0
 * @create date: 2022-02-28
 * @modify date: 2022-03-20
 * @description: Updates images which has been changed in korona article
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class ShopifyProductUpdateImages
{

    // utils object
    protected $utils = null;

    // korona article
    protected $koronaProduct = null;

    // shopify product
    protected $shopifyProduct = null;

    // images directory
    protected $imagesDir = '';

    // images data directory
    protected $dataDir = '';

    // article info
    protected $article = '';

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function updateImages(object $koronaProduct) : void
    {
        // note: even the mediaurls is an array, the user interface in korona allows only 1 image to be used
        // nevertheless use a loop for the case the ui changes

        // init vars
        $position = 0;

        // update class var
        $this->koronaProduct = $koronaProduct;

        // set the article info for logs
        $this->article = '#' . $this->koronaProduct->number . ' (' . $this->koronaProduct->name . '): ';

        // get the shopify product
        $this->shopifyProduct = $this->koronaProduct->shopifyProduct;       
        
        // set the data directory
        $this->dataDir = dirname(__DIR__) . '/data/images/' . $this->koronaProduct->number . '/';

        // set the images directory
        $this->imagesDir = dirname(__DIR__) . '/images/' . $this->koronaProduct->number . '/';
        
        // no images?
        // note: do not check here for shopifyImages! If they were not set in the Image class, but images in
        // korona are set, they will be deleted in shopify
        if ( ! isset($this->koronaProduct->mediaUrls)) {
            
            // delete all images from the shopify product
            // $this->deleteImages();

        // article images in korona, but no shopify images?
        } elseif (isset($this->koronaProduct->shopifyImages) && count($this->shopifyProduct->images) === 0) {

            // add article images to shopify
            $this->addImages();

        } else {

            // shopify images given?
            if (isset($this->koronaProduct->shopifyImages)) {

                // loop through the images
                foreach ($this->koronaProduct->shopifyImages as $shopifyImage) {
                    
                    // get the image object
                    $image = $this->getImage($shopifyImage);
                    
                    // check hash values
                    if ($this->checkHashes($image->position) === false) {
                        
                        // replace the image with the new one
                        $this->updateImage($koronaProduct, $image);
                        
                    } else {

                        // log info
                        $this->utils->logInfo('Image "' . $image->alt . '" already up to date.');

                    }

                }

            }

        }
               
	}

    protected function deleteImages() : void
    {

        // prevent deleting images by accident
        die('remove this line in the ShopifyProductUpdateImages class');

        // grab all hash files in the directory
        $hashFiles = glob($this->dataDir . '*hash.txt');

        // loop through the hash files
        foreach ($hashFiles as $hasFile) {

            // delete the hash file
            @unlink($hashFile);

        }

        // nothing to delete?
        if (count($this->shopifyProduct->images) === 0) {

            // log info
            $this->utils->logInfo('No images for this product found');

            // nothing more todo
            return;

        }
        // delete the images in shopify
        foreach ($this->shopifyProduct->images as $image) {

            // for the log get the image name
            $imageName = $this->getImageName($image->src);

            // delete the image
            $result = $this->utils->curlExec('shopify', 'DELETE', '', 'products/' . $image->product_id . '/images/' . $image->id . '.json');

            // on success it returns the image
            if ($result === '') {

                // increase error counter
                $this->utils->errors++;

                // log error
                $this->utils->logInfo($this->article . 'Error: Could not delete image "' . $image->alt . '" (' . $imageName . ')', null, true);

            } else {

                // increase counter
                $this->utils->imagesDeleted++;

                // log info
                $this->utils->logInfo($this->article . 'Image "' . $image->alt . '" (' . $imageName . ')' . ' deleted.', null, true);

            }
        
        }            

    }

    protected function addImages() : void
    {

        // loop through the images
        foreach ($this->koronaProduct->shopifyImages as $shopifyImage) {

            // get the image data
            $image = json_decode($shopifyImage);

            // get the image name
            $imageName = $this->getImageName($image->src);

            // set the body to create the image
            $body = '{"image":' . $shopifyImage . '}';

            // log info
            $this->utils->logInfo('Body for image creation: ' . $body);

            // add the image to the product
            $result = $this->utils->curlExec('shopify', 'POST', $body, 'products/' . $this->shopifyProduct->id . '/images.json');

            // on success it returns the image
            if (isset($result->image)) {

                // increase counter
                $this->utils->imagesCreated++;

                // log info
                $this->utils->logInfo($this->article . 'Image "' . $image->alt . '" (' . $imageName . ') created in Shopify', null, true);

                // save hash value
                $this->saveHash($image);

            } else {

                // increase error counter
                $this->utils->errors++;

                // log info
                $this->utils->logInfo($this->article . 'Error: Could not create image "' . $image->alt . '" (' . $imageName . ') in Shopify', null, true);

            }
                    
        }

    }
    
    protected function updateImage(object $koronaProduct, object $image) : void
    {

        // init vars
        $found = false;

        // search for the image
        foreach ($this->shopifyProduct->images as $shopifyImage) {

            // position found?
            if ($shopifyImage->position === $image->position) {

                // change found var
                $found = true;

                // leave the loop
                break;

            }

        }

        // image id found?
        if ($found === true) {

            // replace the image
            $this->replaceImage($koronaProduct, $shopifyImage, $image);

        } else {

            // increase error var
            $this->utils->errors++;

            // log error
            $this->utils->logInfo($this->article . 'Error: Shopify product image not found: ' . $image->alt, null, true);

        }

    }

    protected function replaceImage(object $koronaProduct, object $shopifyImage, object $image) : void
    {

        // log info
        $this->utils->logInfo('Getting current image info for Shopify product image on position ' . $image->position . '...');

        // get current image info
        $updatedAt = $this->getImageInfo($shopifyImage);

        // log info
        $this->utils->logInfo('Replacing Shopify product image on position ' . $image->position . '...');

        // get the image data as string
        $imageInfo = json_encode($image);

        // problem?
        if ($imageInfo === '') {

            // increase error counter
            $this->utils->errors++;

            // log error
            $this->utils->logInfo($this->article . 'Error: Could not get image info for product image on position ' . $image->position);

            // nothing more to do here
            return;
            
        }

        // put image info into an image tag
        $body = '{"image":' . $imageInfo . '}';

        // log info
        $this->utils->logInfo('Update body: ' . $body);

        // change the image
        $result = $this->utils->curlExec('shopify', 'PUT', $body, 'products/' . $shopifyImage->product_id . '/images/' . $shopifyImage->id . '.json');

        // on success it returns the image
        if ( ! isset($result->image)) {

            // increase error counter
            $this->utils->errors++;

            // log error
            $this->utils->logInfo($this->article . 'Error: Could not update image "' . $image->alt . '" (id ' . $shopifyImage->id . ')', null, true);

        } elseif ($updatedAt !== '' && $updatedAt !== $result->image->updated_at) {

            // increase counter
            $this->utils->imagesUpdated++;

            // log info
            $this->utils->logInfo($this->article . 'Image "' . $image->alt . '" (id ' . $shopifyImage->id . ') updated.', null, true);

            // save hash value
            $this->saveHash($image);

        } else {

            // increase warning counter
            $this->utils->warnings++;

            // log info
            $this->utils->logInfo('Warning: ' . $this->article . 'Image "' . $image->alt . '" (id ' . $shopifyImage->id . ') may not be updated - trying to update it next time again.', null, true);

        }

    }

    protected function getImageInfo(object $shopifyImage) : string
    {

        // retrieve image info
        $result = $this->utils->curlExec('shopify', 'GET', '', 'products/' . $shopifyImage->product_id . '/images/' . $shopifyImage->id . '.json');

        // problem?
        if ( ! isset($result->image->updated_at)) {

            // increase warning counter
            $this->utils->warnings++;

            // log problem
            $this->utils->logInfo('Warning: ' . $this->article . 'Could not get last updated date from Shopify for product image ' . $shopifyImage->id . ' - trying to update it nevertheless', null, true);

            // return empty string
            return '';

        }

        // return the updated date
        return $result->image->updated_at;

    }

    protected function getImageName(string $src) : string
    {

        // "name" is part of the image source
        $imageName = basename($src);

        // find a question mark
        $q = strpos($imageName, '?');

        // if found, cut image version
        if ($q > -1) $imageName = substr($imageName, 0, $q);

        // return the name
        return $imageName;

    }

    protected function checkHashes(int $position) : bool
    {

        // init vars
        $check = false;

        // set the hash file in the data directory
        $dataHashFile = $this->dataDir . $this->koronaProduct->number . '-' . $position . '-hash.txt';

        // set the hash file in the images directory
        $imageHashFile = $this->imagesDir . $this->koronaProduct->number . '-' . $position . '-hash.txt';
        
        // both files exists?
        if (file_exists($dataHashFile) && file_exists($imageHashFile)) {

            // read its contents
            $dataHash  = file_get_contents($dataHashFile);
            $imageHash = file_get_contents($imageHashFile);

            // if hash values are identically, no update is required
            if ($dataHash === $imageHash) $check = true;
            
        }

        // return the result
        return $check;

    }

    protected function getImage(string $shopifyImage) : object
    {

        // init vars
        $image = new \stdClass();

        // get the image data
        $image = json_decode($shopifyImage);

        // return the result
        return $image;

    }

    protected function saveHash(object $image) : void
    {

        // get the path to the image file
        $imageFile = dirname(__DIR__) . '/images/' . $this->koronaProduct->number . '/' . basename($image->src);

        // save hash value in the data directory
        $this->utils->saveHash($imageFile, $this->koronaProduct->number, $image->position, 'data/images');

    }

}