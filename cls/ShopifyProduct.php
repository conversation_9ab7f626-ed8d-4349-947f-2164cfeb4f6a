<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.0.0
 * @create date: 2022-01-07
 * @modify date: 2022-01-30
 * @description: Search for a product in Shopify with the article number from korona. If found, calls the
 *               price update and/or stock amount update method
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class ShopifyProduct
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function checkProduct(object $koronaProduct) : void
    {

        // get the sku
        $sku = $koronaProduct->number;

        // check if product exists in shopify
        $shopifyProduct = $this->findProduct($sku);

        // product multiple times found?
        if ($shopifyProduct->items > 1) {

            // log info
            $this->utils->logInfo('Product with sku ' . $sku . ' found ' . $shopifyProduct->items . ' times in shopify');

            // nothing more to do here
            return;

        }
        
        // product found?
        if ($shopifyProduct->items === 1) {

            // add the shopify product to the korona product object
            $koronaProduct->shopifyProduct = $shopifyProduct;

            // update the product
            $this->updateProduct($koronaProduct);

        // stock amount update runs after the price update, so product must be created only once
        } elseif ($koronaProduct->sync === 'price') {

            // create new product
            $this->utils->shopifyUtils->createProduct($koronaProduct);

            // update the stock amount?
            if ($this->utils->get('shopify', 'updateStock') === true) {

                // add the stock amount to the korona product
                $this->utils->koronaUtils->setStockAmount($koronaProduct);

                // create a new inventory level item
                $this->utils->shopifyUtils->createStockAmount($koronaProduct);
                
            }

        } elseif ($koronaProduct->sync === 'stock') {

            // log info
            $this->utils->logInfo('No shopify product found for sku ' . $sku);

        }       

    }

    protected function updateProduct(object $koronaProduct) : void
    {

        // update the price?
        if ($koronaProduct->sync === 'price') {

            // call the update method
            $this->utils->shopifyUtils->updateProduct($koronaProduct);

        } elseif ($koronaProduct->sync === 'stock') {

            // call the update method
            $this->utils->shopifyUtils->updateStock($koronaProduct);

        } elseif ($koronaProduct->sync === 'image') {

            // call the update method
            $this->utils->shopifyUtils->updateImages($koronaProduct);

        }
        
	}

    public function findProduct(string $sku) : object
    {

        // init vars
        $shopifyProduct = new \stdClass(); $items = 0;

        // loop through the shopify array
        foreach ($this->utils->shopifyProducts as $shopifyProductItem) {

            // get the product variants
            $variants = $shopifyProductItem->variants;

            // loop through the variants
            foreach ($variants as $variant) {

                // sku found?
                if ($variant->sku === $sku) {

                    // increase items counter to detect multiple items with the same sku
                    $items++;

                    // get the shopify product
                    $shopifyProduct = $shopifyProductItem;

                    // leave the inner loop
                    break;

                }

            }

        }

        // add the number of items to the object
        $shopifyProduct->items = $items;
                    
        // return the product
        return $shopifyProduct;

    }

}