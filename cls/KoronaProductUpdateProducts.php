<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.0
 * @create date: 2022-01-07
 * @modify date: 2022-03-22
 * @description: Detects the product price for the online shop and updates all product prices
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class KoronaProductUpdateProducts
{

    // utils object
    protected $utils = null;
    
    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;
        
    }

    public function updateProducts() : void
    {
        
        // init vars
        $maxRevision = 0; $item = 0; $items = 0;

        // add log info
        $this->utils->logInfo('Start updating price values...');

        // get the latest revision number for the product stocks
        $revision = $this->utils->getRevision('products');

        // update max revision var for the case no items where changed and so the var would not be updated
        $maxRevision = $revision;
        
        // log info
        $this->utils->logInfo('Current products revision: ' . $revision);

        // get the number of product items
        $items = count($this->utils->koronaProducts);

        // loop through the products
        foreach ($this->utils->koronaProducts as $koronaProduct) {

            // increase item counter
            $item++;

            // log progress
            $this->utils->logInfo('Processing article #' . $koronaProduct->number . ' (' . $item . '/' . $items . ')');

            // skip inactive products
            if ($koronaProduct->deactivated === true) {
                
                // log info
                $this->utils->logInfo('Skipped deactivated article');

                // process next item
                continue;

            }
            
            // skip not changed products
            if (intval($koronaProduct->revision) <= $revision) {
                
                // log info
                $this->utils->logInfo('Skipped not changed article (article revision ' . $koronaProduct->revision . ' is smaller than current revision ' . $revision . ')');

                // process next item
                continue;

            }
            
            
             

            // revision number higher then the current?
            if (intval($koronaProduct->revision) > $maxRevision) {

                // set new max value
                $maxRevision = intval($koronaProduct->revision);

            }

            // add additional data to the product
            $this->addProductData($koronaProduct);

            // skip products without prices
            if ( ! isset($koronaProduct->price)) {
                
                // increase warning counter
                $this->utils->warnings++;

                // log info
                $this->utils->logInfo('Warning: Skipped item without any price information', null, true);

                // process next item
                continue;

            }

            // check if product needs update or must be created in shopify
            $this->utils->shopifyUtils->checkProduct($koronaProduct);

        }

        // set new max revision only if update was not forced, since it is 0 in this case
        if ($this->utils->get('korona', 'forceUpdate') !== true) {

            // log info
            $this->utils->logInfo('New products revision: ' . $maxRevision);

            // save the revision for the next request (after updating/creating products)
            $this->utils->setRevision('products', $maxRevision);
            
        }
        
        // add log info
        $this->utils->logInfo('Price values updated.');

    }

    protected function addProductData(object &$koronaProduct) : void
    {

        // set the online shop price
        $this->setProductPrice($koronaProduct);

        // set synchronization type
        $koronaProduct->sync = 'price';

    }

    protected function setProductPrice(object &$koronaProduct) : void
    {

        // try to get the latest online price
        if ($this->getLatestPrice($koronaProduct, 'online') === true) return;

        // try to get the latest standard price
        if ($this->getLatestPrice($koronaProduct, 'standard') === true) return;

        // log error and die
        $this->utils->logError('No price information found for article with sku #' . $koronaProduct->number . ' (' . $koronaProduct->name . ')', $koronaProduct, true);
    
    }

    protected function getLatestPrice(object &$koronaProduct, string $name) : bool
    {

        // init vars
        $found = false;

        // no prices at all given?
        if ( ! isset($koronaProduct->prices[0])) return false;

        // get the prices array
        $prices = $koronaProduct->prices;

        // sort the array by date (latest on last place)
        array_multisort(array_column($prices, 'validFrom'), SORT_ASC, SORT_REGULAR, $prices);

        // loop through the prices
        foreach ($prices as $price) {

            // price found?
            if (strpos(strtolower($price->priceGroup->name), $name) > -1) {

                // use the online price as price
                $koronaProduct->price = $price->value;

                // price found
                $found = true;

            }

        }

        // return the result
        return $found;

    }
        
}