<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.2
 * @create date: 2022-03-08
 * @modify date: 2022-04-01
 * @description: Deletes outdated log files and empty directories
 */

 // no direct access
 defined('GM_EXEC') or die('Access denied');

class CleanLogs
{

    // utils object
    protected $utils = null;

    // dry run
    protected $dryRun = false;

    // result
    protected $result = '';

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

        // reset result
        $this->result = '';
        
    }

    public function cleanDirectory(string $dir) : string
    {

        // get all directories in the given directory
        $it = new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS);

        // get all items in the directories
        $items = new RecursiveIteratorIterator($it, RecursiveIteratorIterator::CHILD_FIRST);

        // loop through the items
        foreach ($items as $item) {
            
            // make sure we are in the logs directory
            if (strpos($item, '/logs/') === false) continue;
            
            // item is a directory?
            if ($item->isDir()) {

                // delete the directory if it is empty
                $this->deleteDirectory($item);

            // outdated file?
            } elseif ($this->outdatedItem($item) === true) {

                // delete the file
                $this->deleteFile($item);

            }
            
        }

        // return the result
        return $this->result;

    }

    protected function outdatedItem(object $item) : bool
    {

        // define outdated time (curl = 3 days, other directories = 14 days)
        $time = (strpos(basename($item), 'curl') > -1) ? 60 * 60 * 24 * 3 : 60 * 60 * 24 * 14;

        // get the last modified time of this item
        $fileMtime = filemtime($item);

        // outdated item?
        if ($fileMtime + $time < time()) {

            // item outdated
            return true;

        } else {

            // item not outdated
            return false;

        }

    }

    protected function deleteOutdatedItem($item) : void
    {

        // dry run only?
        if ($this->dryRun === true) {

            // update info only
            $this->result .= 'Outdated: ' . $item . "\n\n";

        } else {

            // delete the file
            $this->deleteFile($item);

        }

    }

    protected function deleteFile($item) : void
    {

        // delete log files only (there are also .htaccess and index.html files in the logs directory)
        if (strpos($item, '.log') === false) return;

        // delete file
        if (@unlink($item) === true) {

            // update info
            $this->result .= 'Deleted: ' . $item . "\n\n";

        } else {

            // update info
            $this->result .= 'Could not delete: ' . $item . "\n\n";

        }

    }

    protected function deleteDirectory($item) : void
    {
        
        // only delete empty directories
        if ($this->directoryIsEmpty($item) !== true) return;

        // remove the directory
        if ($this->removeDirectory($item) === true) {

            // update the result
            $this->result .= 'Directory deleted: ' . $item . "\n\n";

        } else {

            // update the result
            $this->result .= 'Could not delete directory: ' . $item . "\n\n";

        }

    }

    private function directoryIsEmpty($item) : bool
    {

        // get the items in the directory
        // note: glob does not find hidden files
        // $items = glob($item . '/*');
        $items = scandir($item . '/');
        
        // items found?
        // note: scandir always returns . and ..
        if (count($items) > 2) {

            // on dry run update the result
            if ($this->dryRun === true) $this->result .= 'Directory not empty: ' . $item . "\n\n";

            // directory is not empty
            return false;

        } else {

            // on dry run update the result
            if ($this->dryRun === true) $this->result .= 'Directory empty: ' . $item . "\n\n";
            
            // directory is empty
            return true;
            
        }

    }

    protected function removeDirectory($dir) : bool
    {

        // no directory given?
        if (empty($dir) || ! file_exists($dir)) return false;

        // get all directories in the given directory
        $it = new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS);

        // get all items in the directories
        $items = new RecursiveIteratorIterator($it, RecursiveIteratorIterator::CHILD_FIRST);

        // loop through the items
        foreach ($items as $item) {

            // change rights, so we can delete it
            @chmod($item->getPathname(), 0755);

            // item is a directory?
            if ($item->isDir()) {

                if ( ! @rmdir($item->getPathname())) {

                    // return error
                    return false;

                }

            } else {

                if ( ! @unlink($item->getPathname())) {

                    // return error
                    return false;

                }

            }

        }

        // remove the given directory
        if ( ! @rmdir($dir)) {

            // return error
            return false;

        }

        // reached this point means deleting was ok
        return true;

    }

}