<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.0.0
 * @create date: 2022-01-07
 * @modify date: 2022-02-15
 * @description: Generates a seo friendly slug from a text (product title = image name)
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class Slug
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function getSlug(object $object) : string
    {

        // get the data from the object
        $text = $object->text;
        $maxLength = $object->maxLength ?? 0;
// $this->utils->outputResult($maxLength);
        // init vars
        $slug = '';

        $numbers = array("0","1","2","3","4","5","6","7","8","9","-");
        // $numbers = array("0","1","2","3","4","5","6","7","8","9");

        // replace & sign
        $text = str_replace(' & ', ' und ', $text);
        $text = str_replace('&', ' und ', $text);

        // replace + sign
        $text = str_replace(' + ', ' plus ', $text);
        $text = str_replace('+', ' plus ', $text);

        // remove non latin characters from the given text
        $text = $this->noDiacritics($text);
        
        // create an array with every character
        $characters = str_split($text);

        // loop through the array
        foreach ($characters as $character) {

            // character matches a-z and/or a defined number?
            if (in_array($character, range("a", "z")) || in_array($character, $numbers)) {

                // add the character to the slug
                $slug .= $character;

            // space?
            } elseif ($character === ' ') {

                // add hyphen instead of a space to the slug
                $slug .= "-";

            }

        }

        // max length given?
        if ($maxLength > 0) {

            // cut the slug to the maximum length
            $slug = substr($slug, 0, $maxLength);

        }

        // remove multiple hyphens
        $slug = str_replace('----', '-', $slug);
        $slug = str_replace('---', '-', $slug);
        $slug = str_replace('--', '-', $slug);

        // remove hyphen at the beginning
        if (substr($slug, 0, 1) === '-') $slug = substr($slug, 1);
        
        // check if we have a valid slug
        if ($this->checkSlug($slug) !== true) $slug = '';
        
        // return the slug
        return $slug;

    }

    protected function checkSlug(string $slug)
    {
        return (preg_match("/^[a-zA-Z0-9]+[a-zA-Z0-9\-]*$/", $slug) == 1);
    }

    protected function noDiacritics(string $text) : string
    {

        //cyrylic transcription
        $cyrylicFrom = array('А', 'Б', 'В', 'Г', 'Д', 'Е', 'Ё', 'Ж', 'З', 'И', 'Й', 'К', 'Л', 'М', 'Н', 'О', 'П', 'Р', 'С', 'Т', 'У', 'Ф', 'Х', 'Ц', 'Ч', 'Ш', 'Щ', 'Ъ', 'Ы', 'Ь', 'Э', 'Ю', 'Я', 'а', 'б', 'в', 'г', 'д', 'е', 'ё', 'ж', 'з', 'и', 'й', 'к', 'л', 'м', 'н', 'о', 'п', 'р', 'с', 'т', 'у', 'ф', 'х', 'ц', 'ч', 'ш', 'щ', 'ъ', 'ы', 'ь', 'э', 'ю', 'я');

        $cyrylicTo   = array('A', 'B', 'W', 'G', 'D', 'Ie', 'Io', 'Z', 'Z', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'R', 'S', 'T', 'U', 'F', 'Ch', 'C', 'Tch', 'Sh', 'Shtch', '', 'Y', '', 'E', 'Iu', 'Ia', 'a', 'b', 'w', 'g', 'd', 'ie', 'io', 'z', 'z', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'r', 's', 't', 'u', 'f', 'ch', 'c', 'tch', 'sh', 'shtch', '', 'y', '', 'e', 'iu', 'ia'); 


        $from = array("Á", "À", "Â", "Ä", "Ă", "Ā", "Ã", "Å", "Ą", "Æ", "Ć", "Ċ", "Ĉ", "Č", "Ç", "Ď", "Đ", "Ð", "É", "È", "Ė", "Ê", "Ë", "Ě", "Ē", "Ę", "Ə", "Ġ", "Ĝ", "Ğ", "Ģ", "á", "à", "â", "ä", "ă", "ā", "ã", "å", "ą", "æ", "ć", "ċ", "ĉ", "č", "ç", "ď", "đ", "ð", "é", "è", "ė", "ê", "ë", "ě", "ē", "ę", "ə", "ġ", "ĝ", "ğ", "ģ", "Ĥ", "Ħ", "I", "Í", "Ì", "İ", "Î", "Ï", "Ī", "Į", "Ĳ", "Ĵ", "Ķ", "Ļ", "Ł", "Ń", "Ň", "Ñ", "Ņ", "Ó", "Ò", "Ô", "Ö", "Õ", "Ő", "Ø", "Ơ", "Œ", "ĥ", "ħ", "ı", "í", "ì", "i", "î", "ï", "ī", "į", "ĳ", "ĵ", "ķ", "ļ", "ł", "ń", "ň", "ñ", "ņ", "ó", "ò", "ô", "ö", "õ", "ő", "ø", "ơ", "œ", "Ŕ", "Ř", "Ś", "Ŝ", "Š", "Ş", "Ť", "Ţ", "Þ", "Ú", "Ù", "Û", "Ü", "Ŭ", "Ū", "Ů", "Ų", "Ű", "Ư", "Ŵ", "Ý", "Ŷ", "Ÿ", "Ź", "Ż", "Ž", "ŕ", "ř", "ś", "ŝ", "š", "ş", "ß", "ť", "ţ", "þ", "ú", "ù", "û", "ü", "ŭ", "ū", "ů", "ų", "ű", "ư", "ŵ", "ý", "ŷ", "ÿ", "ź", "ż", "ž");

        $to   = array("A", "A", "A", "AE", "A", "A", "A", "A", "A", "AE", "C", "C", "C", "C", "C", "D", "D", "D", "E", "E", "E", "E", "E", "E", "E", "E", "G", "G", "G", "G", "G", "a", "a", "a", "ae", "ae", "a", "a", "a", "a", "ae", "c", "c", "c", "c", "c", "d", "d", "d", "e", "e", "e", "e", "e", "e", "e", "e", "g", "g", "g", "g", "g", "H", "H", "I", "I", "I", "I", "I", "I", "I", "I", "IJ", "J", "K", "L", "L", "N", "N", "N", "N", "O", "O", "O", "OE", "O", "O", "O", "O", "CE", "h", "h", "i", "i", "i", "i", "i", "i", "i", "i", "ij", "j", "k", "l", "l", "n", "n", "n", "n", "o", "o", "o", "oe", "o", "o", "o", "o", "o", "R", "R", "S", "S", "S", "S", "T", "T", "T", "U", "U", "U", "UE", "U", "U", "U", "U", "U", "U", "W", "Y", "Y", "Y", "Z", "Z", "Z", "r", "r", "s", "s", "s", "s", "ss", "t", "t", "b", "u", "u", "u", "ue", "u", "u", "u", "u", "u", "u", "w", "y", "y", "y", "z", "z", "z");

        // merge the arrays
        $from = array_merge($from, $cyrylicFrom);
        $to   = array_merge($to, $cyrylicTo);

        // replace non latin characters from the text
        $text = str_replace($from, $to, $text);

        // return the cleaned text in lower case
        return strtolower($text);

    }
        
}