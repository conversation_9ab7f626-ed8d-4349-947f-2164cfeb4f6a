<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.0.3
 * @create date: 2022-01-07
 * @modify date: 2022-03-21
 * @description: Gets all changed stock items from Korona and all stock items from Shopify.
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class KoronaProductUpdateStocks
{

    // utils object
    protected $utils = null;

    // all product stocks items of an organization unit
    protected $productStocks = [];

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function updateStocks(bool $force=false) : void
    {

        // init vars
        $revision = 0; $maxRevision = 0; $item = 0; $items = 0;

        // log info (already done in the index.php if all stock items will be updated)
        if ($force !== true) $this->utils->logInfo('Start updating stock amount...');

        // fetch the inventory from shopify
        $this->utils->shopifyUtils->getShopifyInventoryItems();

        // log info
        $this->utils->logInfo('Fetched Shopify inventory items: ' . count($this->utils->shopifyInventoryItems));
        
        // get the latest revision number for the product stocks
        // if we force to update all, do not do this, since we need a 0 revision number
        if ($force !== true) $revision = $this->utils->getRevision('productStocks');

        // update max revision var for the case no items where changed and so the var would not be updated
        $maxRevision = $revision;

        // log info
        if ($force !== true) $this->utils->logInfo('Current product stocks revision: ' . $revision);

        // get the product stocks to get the stock amount value
        $this->getProductStocks($revision, 1);
        
        // log info
        $this->utils->logInfo('Fetched Korona product stocks: ' . count($this->utils->koronaProductStocks));

        // get the number of product stock items
        $items = count($this->utils->koronaProductStocks);

        // loop through the product stocks
        foreach ($this->utils->koronaProductStocks as $productStock) {

            // revision number higher then the current?
            if (intval($productStock->revision) > $maxRevision) {

                // set new max value
                $maxRevision = intval($productStock->revision);

            }
            
            // get the product for this stock item
            $koronaProduct = $this->getProduct($productStock);

            // increase item counter
            $item++;

            // log progress
            $this->utils->logInfo('Processing stock item ' . $item . '/' . $items);

            // problem?
            if ( ! isset($koronaProduct->id)) {
                
                // log info
                $this->utils->logInfo('Skipped (no item for the online shop)');

                // process next item
                continue;

            }

            // $this->utils->outputResult($koronaProduct);
            
            // product for the online shop?
            if ($this->onlineProduct($koronaProduct) === true) {
            
                // check if product needs update
                $this->utils->shopifyUtils->checkProduct($koronaProduct);

            } else {

                // log info
                $this->utils->logInfo('Product with sku ' . $koronaProduct->number . ' is no item for the online shop');

            }

        }

        // set new max revision only if update was not forced, since it is 0 in this case
        if ($this->utils->get('korona', 'forceUpdate') !== true && $force !== true) {

            // log info
            $this->utils->logInfo('New product stocks revision: ' . $maxRevision);

            // save the revision for the next request (after updating/creating products)
            $this->utils->setRevision('productStocks', $maxRevision);      
        
        }

        // log info (already done in the index.php if all stock items will be updated)
        if ($force !== true) $this->utils->logInfo('Stock amount updated.');

        // $this->utils->outputResult($result);

    }
    
    protected function getProductStocks(string $revision, int $page=1)
    {

        // read 1000 items of the product stocks of the organization
        $result = $this->utils->curlExec('korona', 'GET', '', 'organizationalUnits/' . $this->utils->koronaOrganizationId . '/productStocks?size=1000&revision=' . $revision . '&page=' . $page);

        // if no more items are found, the result is empty
        if ($result === '') return;

        // problem?
        if ( ! isset($result->results)) {

            // log error and die
            $this->utils->logError('Can not read product stocks for organization id ' . $this->utils->koronaOrganizationId, $result, true);

        }

        // merge the result with the products array to avoid getting another array for each page in the products array
        $this->utils->koronaProductStocks = array_merge($this->utils->koronaProductStocks, $result->results);

        // another page given?
        if ($result->currentPage < $result->pagesTotal) {

            // increase the page counter
            $page++;

            // fetch the next page (< 25 prevents endless loop)
            if ($page < 25) $this->getProductStocks($revision, $page);

        }

    }

    protected function getProduct(object $productStock) : object
    {

        // get the product id
        $productId = $productStock->product->id;
        
        // get the product index
        $index = array_search($productId, array_column($this->utils->koronaProducts, 'id'));

        // get the product object
        $koronaProduct = $this->utils->koronaProducts[$index];
        // foreach ($this->utils->koronaProducts as $koronaProduct) {

        //     // if product was found leave the loop
        //     if ($koronaProduct->id === $productId) break;
            
        // }

        // problem?
        if ( ! $koronaProduct || $koronaProduct->id !== $productId) {

            // return empty object
            return new \stdClass();

        }

        // add product stock data
        $koronaProduct->stockAmount = $productStock->amount->actual;
        
        // set synchronization type
        $koronaProduct->sync = 'stock';
        
        // return the product
        return $koronaProduct;

    }

    protected function onlineProduct(object $koronaProduct) : bool
    {

        // init vars
        $onlineProduct = false;

        // get the tags
        $tags = $koronaProduct->tags;
        
        // loop through the tags
        foreach ($tags as $tag) {

            // online product?
            if (strpos(strtolower($tag->name), 'online') > -1) {

                // change var
                $onlineProduct = true;

                // leave the loop
                break;

            }

        }
        
        // return the result
        return $onlineProduct;

    }

    public function setStockAmount(object &$koronaProduct) : void
    {

        // init vars
        $stockAmount = 0; $revision = 0;

        // read the product stocks for the product
        $result = $this->utils->curlExec('korona', 'GET', '', 'products/' . $koronaProduct->id . '/stocks');

        // no stock items found?
        if ($result === '' || ! isset($result->results)) {

            // increase warning counter
            $this->utils->warnings++;

            // no stock items
            $koronaProduct->stockAmount = 0;

            // log info
            $this->utils->logInfo('Warning: No stock items found for product #' . $koronaProduct->number . ' in Korona', null, true);

            // nothing more to do here
            return;

        }

        // loop through the results
        foreach ($result->results as $result) {

            // item for our organization?
            if ($result->warehouse->id === $this->utils->koronaOrganizationId) {

                // revision higher than current revision?
                if ($result->revision > $revision) {

                    // update the revision
                    $revision = $result->revision;
                    
                    // update the stock amount
                    $stockAmount = $result->amount->actual;

                }


            }

        }
        
        // set the stock amount
        $koronaProduct->stockAmount = $stockAmount;

        // log info
        $this->utils->logInfo('Stock amount for article #' . $koronaProduct->number . ' set to ' . $stockAmount);

    }

}