<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.0.0
 * @create date: 2022-01-07
 * @modify date: 2022-01-30
 * @description: Implements an autoload function for our own classes and creates an object from the Utils class
 */

 // define access deny var
define('GM_EXEC', 1);

// directory separator
define('DS', DIRECTORY_SEPARATOR);

// show errors
ini_set('display_errors', '1');
ini_set('display_startup_errors', '1');

// register autoload function for own class files
spl_autoload_register(function($className)
{
    
    // set the path to the normal class file
    $file = __DIR__ . DS . $className . '.php';
    
    // exits?
    if (is_readable($file)) {
        
        // use it
        require $file;
        
    } else {
        
        // set the path to the config class directory
        $file = dirname(__DIR__) . DS . 'config' . DS . $className . '.php';
        
        // exists?
        if (is_readable($file)) {
            
            // use it
            require $file;
            
        }
        
    }
});

// create a new instance of the utils class
$utils = new Utils();