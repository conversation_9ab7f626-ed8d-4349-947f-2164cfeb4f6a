<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.1
 * @create date: 2022-01-07
 * @modify date: 2022-03-18
 * @description: <PERSON>les functions around orders in Shopify, in first place it finds new/changed order
 *               for the order synchronization with Korona.
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class ShopifyOrders
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function getOrders(string $cursor='', int $break=0) : void
    {

        // init vars
        $items = 250;

        // get the last sync date/time object
        $date = $this->utils->getLastSyncTime();

        // set the compare date/time
        $compareDate = new DateTime($date);
        
        // set the body to find the product
        // https://shopify.dev/api/usage/rate-limits
        // https://shopify.dev/api/usage/search-syntax
        // https://shopify.dev/api/usage/pagination-graphql
        // https://shopify.dev/api/admin-graphql/2022-01/queries/orders        
        // note: the query data must be in 1 line, otherwise a bad request will be returned
        // important: only orders not older than 60 days can be retrieved, otherwise you have
        //            to contact the support to extend this limitation.

        // cursor given (only after the first call)
        if ($cursor !== '') {

            // use body with pagination
            $body = '{
                "query":
                "query {orders(first: ' . $items . ', after:\"' . $cursor . '\")';

        } else {
            
            // use body without pagination 
            $body = '{
                "query":
                "query {orders(first: ' . $items . ', query: \"updated_at:>' . $date . '\")';
            
        }

        // important: do not use this, because it does not include partially fulfilled orders:
        //  AND NOT fulfillment_status:unfulfilled

        // add the rest of the params (metafields(first:1) {edges{node{id key}}})
        //$body .= ' {pageInfo {hasNextPage} edges{cursor node{id name displayFinancialStatus displayFulfillmentStatus updatedAt refunds{id updatedAt} metafields(first:3) {edges{node{id key}}} lineItems(first: ' . $maxLineItems . ') {edges{node{id sku quantity fulfillableQuantity refundableQuantity}}}}}}}"}';

        // $body .= ' {pageInfo {hasNextPage} edges{cursor node{id name displayFinancialStatus displayFulfillmentStatus updatedAt refunds{id updatedAt} lineItems(first: ' . $maxLineItems . ') {edges{node{sku quantity refundableQuantity}}}}}}}"}';

        $body .= ' {pageInfo {hasNextPage} edges{cursor node{id name createdAt displayFinancialStatus displayFulfillmentStatus updatedAt refunds{id updatedAt} fulfillments {id name updatedAt} }}}}"}';

        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'POST', $body, 'graphql.json');

        // $this->utils->outputResult($result);

        // $this->utils->outputResult($result);
        // if ($cursor !== '') $this->utils->outputResult($result);
        // if ($cursor !== '') $this->utils->outputResult($result->data->orders);

        // no orders found?
        if ( ! isset($result->data->orders->edges)) return;

        // important: updated_at is only working with dates not with times in dates
        // see https://community.shopify.com/c/shopify-apis-and-sdks/fetching-updated-products-using-filter-by-updated-at-using/
        // maybe this will be fixed in the future, but we will not relay on this buggy time filtering
        // and use our own filtering function instead
        foreach ($result->data->orders->edges as $edge) {
            
            // skip unfulfilled orders
            if ($this->checkOrderStatus($edge) === false) continue;

            // skip outdated orders
            // note: 2022-03-17 removed the time check to make sure an order will not be processed if a temporary
            // error ocurred
            // if ($this->checkOrderDate($edge, $compareDate) === true) continue;

            // create a normal object without the edges/node stuff
            $order = new \stdClass();

            // add needed data to the object
            $order->id = $edge->node->id;
            $order->name = $edge->node->name;
            $order->date = $edge->node->createdAt;
            // $order->status = $edge->node->order
            $order->financialStatus = $edge->node->displayFinancialStatus;
            $order->fulfillmentStatus = $edge->node->displayFulfillmentStatus;
            $order->updatedAt = $edge->node->updatedAt;
            $order->fulfillments = $edge->node->fulfillments;
            $order->refunds = $edge->node->refunds;            

            // add order data
            if ($this->addOrderData($order) !== true) continue;

            // add the order to the orders array
            $this->utils->shopifyOrders[] = $order;

        }

        // next page given?
        if ($result->data->orders->pageInfo->hasNextPage === true && $break < 100) {

            // increase emergency break counter
            $break++;

            // get the number of edges
            $edges = count($result->data->orders->edges);

            // call this function again with the given cursor of the last edge
            $this->getOrders($result->data->orders->edges[$edges-1]->cursor, $break);

        }
        
    }

    protected function checkOrderStatus(object $edge) : bool
    {

        // https://shopify.dev/api/admin-graphql/2022-01/enums/orderdisplayfulfillmentstatus

        // get the order status in uppercase since the api in shopify sometimes returns different
        // values as in the docs written
        $status = strtoupper($edge->node->displayFulfillmentStatus);

        // fulfilled and/or partially fulfilled order?
        if ($status === 'FULFILLED' || $status === 'PARTIALLY_FULFILLED') {

            // order can be processed
            return true;

        }

        // order can not be processed
        return false;

    }

    protected function addOrderData(object &$order) : bool
    {

        // init vars
        $orderNumber = '';        
        
        // get the path to the orders data file
        $dataFile = $this->getOrderDataFile($order, $orderNumber);

        // file not exists?
        if ( ! file_exists($dataFile)) {

            // create a new order data object
            $orderData = new \stdClass();

            // add needed data to it
            $orderData->number = $orderNumber;
            $orderData->date = $order->date;
            // to sensitive data:
            // $orderData->status = $this->getOrderStatus($order->name);
            $orderData->id = $this->utils->getIdFromGid($order->id);
            $orderData->fulfillments = [];
            $orderData->refunds = [];

            // save the order data to disk
            if ($this->saveOrderData($orderData, $dataFile) !== true) return false;

        } else {

            // try to read the data from the json file
            $orderData = $this->readOrderData($order);

            // error?
            if (is_null($orderData)) return false;

        }

        // add order data to the order
        $order->data = $orderData;

        // process this order
        return true;

    }

    protected function getOrderStatus(string $orderName) : string
    {

        // note: the order status url is not accessible via the graph api

        // init vars
        $status = '';

        // get the order data
        $order = $this->utils->shopifyUtils->getOrder($orderName);

        // found?
        if (isset($order->order_status_url)) {

            // get the status url
            $status = $order->order_status_url;

        }

        // return the result
        return $status;

    }

    protected function getOrderDataFile(object $order, string &$orderNumber) : string
    {

        // get the order number without the hash
        $orderNumber = str_replace('#', '', $order->name);

        // get the date of the created order
        $date = new DateTime($order->date);
        
        // get the orders data directory
        $dataDir = $this->utils->dataOrdersDir . $date->format('Y') . '/' . $date->format('m') . '/' . $date->format('d') . '/';        
                    
        // make sure the path exists
        @mkdir($dataDir, 0700, true);

        // problem?
        if ( ! file_exists($dataDir)) {

            // increase error counter
            $this->utils->errors++;

            // log the error and stop program execution
            $this->utils->logError('Can not create order data directory: ' . $dataDir, $order, true);

        }

        // get the path to the orders data file
        $dataFile = $dataDir . $orderNumber . '.json';

        // return the file
        return $dataFile;

    }

    protected function readOrderData(object &$order) : object
    {

        // init vars
        $orderNumber = ''; $orderData = new \stdClass();

        // get the path to the orders data file
        $dataFile = $this->getOrderDataFile($order, $orderNumber);

        // if no order data file exists return nothing
        if ( ! file_exists($dataFile)) return null;
            
        // read order data
        $jsonData = file_get_contents($dataFile);

        // get an object from it
        $orderData = json_decode($jsonData);

        // error?
        if ($this->utils->checkJson() !== true) return null;

        // return the object
        return $orderData;

    }

    public function setOrderData(object $order) : bool
    {

        // init vars
        $orderNumber = '';

        // get the path to the orders data file
        $dataFile = $this->getOrderDataFile($order, $orderNumber);

        // save data to the file
        if ($this->saveOrderData($order->data, $dataFile) !== true) return false;

        // saving was ok
        return true;

    }

    protected function saveOrderData(object $orderData, string $dataFile) : bool
    {

        // get the json data of it
        $jsonData = json_encode($orderData, JSON_PRETTY_PRINT);

        // error?
        if ($this->utils->checkJson() !== true) return false;

        // save the data into the json file
        if ($this->utils->saveFile($dataFile, $jsonData) !== true) {

            // increase error counter
            $this->utils->errors++;

            // log error
            $this->utils->logInfo('Error: Can not save order info file to ' . $dataFile, $orderData, true);

            // do not process this order
            return false;

        }

        // saving was ok
        return true;

    }

    public function getFulfillments(string $orderId) : array
    {

        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'GET', '', 'orders/' . $orderId . '/fulfillments.json');

        // problem?
        if ( ! isset($result->fulfillments)) {

            // log the error and die
            $this->utils->logError('Can not read fulfillments for order id ' . $orderId, $result, true);

        }

        // return the fulfillments
        return $result->fulfillments;

    }

    // not yet in use
    public function getRefunds(string $orderId) : array
    {
        
        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'GET', '', 'orders/' . $orderId . '/refunds.json');

        // problem?
        if ( ! isset($result->refunds)) {

            // log the error and die
            $this->utils->logError('Can not read refunds for order id ' . $orderId, $result, true);

        }

        // return the refunds
        return $result->refunds;
        

    }  

    public function getRefund(string $orderId, string $refundId) : object
    {

        // get the order id only
        $orderId = $this->utils->getIdFromGid($orderId);

        // get the refund id only
        $refundId = $this->utils->getIdFromGid($refundId);

        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'GET', '', 'orders/' . $orderId . '/refunds/' . $refundId . '.json');

        // problem?
        if ( ! isset($result->refund)) {

            // log the error and die
            $this->utils->logError('Can not read refund with id ' . $refundId . ' for order with id ' . $orderId, $result, true);

        }

        // return the refunds
        return $result->refund;        

    }

    protected function checkOrderDate(object $edge, object $compareDate) : bool
    {

        // get the order date object
        $orderDate = new DateTime($edge->node->updatedAt);

        // force update for certain orders
        // if ($edge->node->name === '43698' || $edge->node->name === '43699') return false;
        // if ($edge->node->name === '43702') return false;

        // return the result
        return $orderDate < $compareDate;

    }

    public function createOrders()
    {

        // set options
        $orders = 3; $items = 3; $quantity = 3;

        // shopify products needed
        $this->utils->shopifyUtils->getShopifyProducts();

        // use a loop to create fake orders
        for ($i = 0; $i < $orders; $i++) {    

            // create the order
            $result = $this->createOrder($items, $quantity);

        }

        // output the result of the last created order
        $this->utils->outputResult($result);

    }

    public function getOrder(string $orderName) : object
    {

        // get all orders
        // note: ?status=any needed to get all orders for the last 60 days
        $result = $this->utils->curlExec('shopify', 'GET', '', 'orders.json?status=any');
        
        // var_export($result);die();
        foreach ($result->orders as $order) {

            // order found?
            if ($order->name === $orderName) {

                // return the order
                return $order;

            }
        }

        // return empty object
        return new \stdClass();

    }

    protected function createOrder(int $items=1, int $quantity=1)
    {

        // set the body to create a new order

        // #10087 3A Bohnen mittel 500g
        // #10086 3A schwarzäugige Bohnen 500g
        // #10079 Agrino dicke Linsen 500g
        switch ($items) {

            case 1: default:

                $lineItems = '{
                    "variant_id":' . $this->getVariantId('10087') . ',
                    "quantity":' . $quantity . '
                }';

                break;

            case 2:

                $lineItems = '{
                    "variant_id":' . $this->getVariantId('10087') . ',
                    "quantity":' . $quantity . '
                },
                {
                    "variant_id":' . $this->getVariantId('10086') . ',
                    "quantity":' . $quantity . '
                }';

                break;

            case 3:

                $lineItems = '{
                    "variant_id":' . $this->getVariantId('10087') . ',
                    "quantity":' . $quantity . '
                },
                {
                    "variant_id":' . $this->getVariantId('10086') . ',
                    "quantity":' . $quantity . '
                },
                {
                    "variant_id":' . $this->getVariantId('10079') . ',
                    "quantity":' . $quantity . '
                }';

                break;

        }

        $body = '{
            "order": {
                "line_items":[
                    ' . $lineItems . '
                ],
                "customer":{
                    "id":5558242574401
                },
                "financial_status":"pending",
                "inventory_behaviour":"decrement_obeying_policy",
                "send_receipt":false,
                "send_fulfillment_receipt":false
            }
        }';

        // execute the curl request
        return $this->utils->curlExec('shopify', 'POST', $body, 'orders.json');

    }
    
    protected function getVariantId(string $sku) : string
    {

        // find the product
        $product = $this->utils->shopifyUtils->findProduct($sku);
        
        // found?
        if ( ! empty($product) && $product->items === 1) {

            // return the id of the first variant
            return $product->variants[0]->id;

        }

        // return empty string
        return '';

    }

    public function deleteOrders()
    {
    
        // warn user before deleting orders
        die('remove this line in the ShopifyOrders class to delete the orders in Shopify');

        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'GET', '', 'orders.json?status=any&fields=id');
        
        // loop through the orders
        foreach ($result->orders as $order) {

            // delete it
            $result = $this->utils->curlExec('shopify', 'DELETE', '', 'orders/' . $order->id . '.json');
        
        }
        
        // show the result and die
        $this->utils->outputResult($result);

    }

}