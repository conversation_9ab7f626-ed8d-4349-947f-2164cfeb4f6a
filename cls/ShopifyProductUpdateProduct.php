<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.2.0
 * @create date: 2022-01-07
 * @modify date: 2022-03-21
 * @description: Read Shopify price from first and only product variant and compare it with the article price
 *               from Korona. If different, update the price in Shopify. Also compares title and description.
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class ShopifyProductUpdateProduct
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function updateProduct(object $koronaProduct) : void
    {

        // get the sku
        $sku = $koronaProduct->number;

        // get the shopify product set in shopify product class
        $shopifyProduct = $koronaProduct->shopifyProduct;
        
        /*
        $date_time = strftime("%Y%m%d %H:%M:%S",time());
                    $fpa = fopen(dirname(__FILE__).'/temp/shopifyProductUpdate_updateProduct - '.$date_time.'.txt', 'w'); 
                    fwrite($fpa, 'Update product'."\r\n");
                    fwrite($fpa, json_encode($koronaProduct)."\r\n" );
                    fwrite($fpa,$koronaProduct->description."\r\n");
                    fwrite($fpa,$koronaProduct->ingredients."\r\n");
                    fwrite($fpa,$shopifyProduct."\r\n");
                    fclose($fpa);
*/
        // no shopify product found?
        if ( ! isset($shopifyProduct)) {

            // log error and die
            $this->utils->outputResult('Can not read shopify product data for #' . $sku . ' (' . $koronaProduct->name . ')');

        }

        // get the first variant (should be only one)
        $shopifyVariant = $shopifyProduct->variants[0];

        // problem?
        if ( ! isset($shopifyVariant)) {

            // log error and die
            $this->utils->logError('Can not read product variant for #' . $sku . ' (' . $koronaProduct->name . ')', null, true);

        }

        // price up to date?
        if (floatval($shopifyVariant->price) === floatval($koronaProduct->price)) {

            // log info
            $this->utils->logInfo('Price for product #' . $sku . ' (' . $koronaProduct->name . ') is already up to date in Shopify (' . $koronaProduct->price . ')');

        } else {

            // update the price
            $this->updatePrice($shopifyVariant, $koronaProduct);        
        
        }
        
       


        // update other data too?
        if ($this->utils->get('shopify', 'updateAll') === true) {

            // get the seo stuff for the korona product to be abel to detected changed titles/descriptions
            $this->utils->koronaUtils->setSeo($koronaProduct);

            // title and description up to date?
            // note: compare the seo title, since it may change while the product title does not
            // important: this is not yet possible without fetching the metafields for each product
            // https://community.shopify.com/c/shopify-apis-and-sdks/is-it-possible-to-change-a-product-s-metafields-global-title-tag/m-p/348842/highlight/false#M18153
            if ($shopifyProduct->title === $koronaProduct->name &&
                $this->compareDescription($shopifyProduct->body_html, $koronaProduct->description) === true) {
                
            // if ($shopifyProduct->title === $koronaProduct->name &&
            //     $shopifyProduct->body_html === $koronaProduct->description &&
            //     $shopifyProduct->metafields_global_title_tag === $koronaProduct->seoTitle &&
            //     $shopifyProduct->metafields_global_description_tag === $koronaProduct->seoDescription) {

                // log info
                $this->utils->logInfo('Title and description for product #' . $sku . ' (' . $koronaProduct->name . ') is already up to date in Shopify');

            } else {

                // update the title and/or the description
                $this->updateTitleAndDescription($shopifyProduct, $koronaProduct);        
            
            }
            
        }
        
	}

    protected function updatePrice(object $shopifyVariant, object $koronaProduct) : void
    {
    
        // get the current prices
        $koronaPrice = $koronaProduct->price;
        $shopifyOldPrice = number_format($shopifyVariant->price, 2, ',', '.');

// var_export($shopifyVariant); echo "$shopifyOldPrice"; die('JJ');

        // set the body to update the price
        $body = '{"variant":{"id":' . $shopifyVariant->id . ',"price":"' . floatval($koronaPrice) . '"}}';
        
        // update the variant price
        $result = $this->utils->curlExec('shopify', 'PUT', $body, 'variants/' . $shopifyVariant->id . '.json');

        // problem?
        if ( ! isset($result) || empty($result) || ! isset($result->variant)) {

            // log error and die
            $this->utils->logError('Can not set price for product #' . $koronaProduct->number . ' (' . $koronaProduct->name . ')', $result, true);

        }

        // update ok?
        if (floatval($result->variant->price) === floatval($koronaPrice)) {

            // update counter
            $this->utils->prices++;

            // log info
            $this->utils->logInfo('Price for product #' . $koronaProduct->number . ' (' . $koronaProduct->name . ') set to ' . number_format($koronaPrice, 2, ',', '.') . ' in Shopify (old price was ' . $shopifyOldPrice . ')', $result->variant->price, true);

        } else {

            // increase warning counter
            $this->utils->warnings++;

            // log info
            $this->utils->logInfo('Warning: Price for product #' . $koronaProduct->number . ' (' . $koronaProduct->name . ') NOT set to ' . number_format($koronaPrice, 2, ',', '.') . ' in Shopify (current price is still ' . $shopifyOldPrice . ')', null, true);

        }

    }

    protected function updateTitleAndDescription(object $shopifyProduct, object $koronaProduct) : void
    {

        // init vars
        $update = false;

        // get the current title/description
        $shopifyOldTitle = $shopifyProduct->title;
        $shopifyOldDescription = $shopifyProduct->body_html;

        // the idiots does not give this fields back
        // https://community.shopify.com/c/shopify-apis-and-sdks/is-it-possible-to-change-a-product-s-metafields-global-title-tag/m-p/348842/highlight/false#M18153
        // $shopifyOldSeoTitle = $shopifyProduct->metafields_global_title_tag;
        // $shopifyOldSeoDescription = $shopifyProduct->metafields_global_description_tag;
      

        // set the body for the update
        $body = '{
            "product": {
                "id":' . $shopifyProduct->id . ',
                "title": "' . $koronaProduct->name . '",
                "body_html":"' . str_replace('"', '\u0022', $koronaProduct->description) . '",
                "metafields_global_title_tag":"' . $koronaProduct->seoTitle . '",
                "metafields_global_description_tag":"' . $koronaProduct->seoDescription . '"
            }
        }';

        // var_export($body2);echo "\n\n"; var_export(json_encode($body));die();
        // $this->utils->outputResult($body);
        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'PUT', $body, 'products/' . $shopifyProduct->id . '.json');
// $this->utils->outputResult($result);
        // problem?
        if ( ! isset($result) || empty($result) || ! isset($result->product)) {

            // log error and die
            $this->utils->logError('Can not update title/description for product #' . $koronaProduct->number . ' (' . $koronaProduct->name . ')' . "\n" . $body, $result, true);

        }
        
        // title needed to be changed?
        if ($shopifyOldTitle !== $koronaProduct->name) {
            
            
        /*    
             $date_time = strftime("%Y%m%d %H:%M:%S",time());
                    $fpa = fopen(dirname(__FILE__).'/temp/shopifyProductUpdateProduct_updateTitleAndDescription - '.$date_time.'.txt', 'w'); 
                    fwrite($fpa, 'Update product');
                    fwrite($fpa, $koronaProduct);
                    fclose($fpa);
        */
            
            

            // title update ok?
            if ($result->product->title === $koronaProduct->name) {

                // set update var
                $update = true;
    
                // log info
                $this->utils->logInfo('Title for product #' . $koronaProduct->number . ' changed (new/old):' . "\n" .  $koronaProduct->name . "\n" . $shopifyOldTitle, null, true);
    
            } else {
    
                // increase warning counter
                $this->utils->warnings++;
                
                // log info
                $this->utils->logInfo('Warning: Title for product #' . $koronaProduct->number . ' NOT set to ' . $koronaProduct->name . ' in Shopify (current title is still ' . $shopifyOldTitle . ')', null, true);
    
            }    

        }

        // // seo title needed to be changed?
        // if ($shopifyOldSeoTitle !== $koronaProduct->seTitle) {

        //     // seo title update ok?
        //     if ($result->product->metafields_global_title_tag === $koronaProduct->seoTitle) {

        //         // set update var
        //         $update = true;
    
        //         // log info
        //         $this->utils->logInfo('Seo title for product #' . $koronaProduct->number . ' set to ' . $koronaProduct->seoTitle . ' in Shopify (old seo title was ' . $shopifyOldSeoTitle . ')', null, true);
    
        //     } else {
    
        //         // log info
        //         $this->utils->logInfo('Warning: Seo title for product #' . $koronaProduct->number . ' NOT set to ' . $koronaProduct->seoTitle . ' in Shopify (current title is still ' . $shopifyOldSeoTitle . ')', null, true);
    
        //     }    

        // }

        // description needs to be changed?
        if (htmlspecialchars_decode($shopifyOldDescription) !== htmlspecialchars_decode($koronaProduct->description)) {

            // description update ok?
            if ($this->compareDescription($result->product->body_html, $koronaProduct->description) === true) {

                // set update var
                $update = true;            

                // log info
                $this->utils->logInfo('Description for product #' . $koronaProduct->number . ' changed (new/old):' . "\n\n" . $koronaProduct->description . "\n\n" . $shopifyOldDescription, null, true);

            } else {

                // increase warning counter
                $this->utils->warnings++;

                // log info
                $this->utils->logInfo('Warning: Description for product #' . $koronaProduct->number . ' may not set to:' . "\n\n" . $koronaProduct->description . "\n\n" . 'in Shopify. Current description is still:' . "\n\n" . $shopifyOldDescription, $result->product->body_html, true);

            }
            
        }

        // // seo description needs to be changed?
        // if ($shopifyOldSeoDescription !== $koronaProduct->seoDescription) {

        //     // description update ok?
        //     if ($result->product->metafields_global_description_tag === $koronaProduct->seoDescription) {

        //         // set update var
        //         $update = true;            

        //         // log info
        //         $this->utils->logInfo('Seo description for product #' . $koronaProduct->number . ' set to ' . $koronaProduct->description . ' in Shopify (old seo description was ' . $shopifyOldSeoDescription . ')', null, true);

        //     } else {

        //         // log info
        //         $this->utils->logInfo('Warning: Seo description for product #' . $koronaProduct->number . ' NOT set to ' . $koronaProduct->seoDescription . ' in Shopify (current seo description is still ' . $shopifyOldSeoDescription . ')', null, true);

        //     }
            
        // }

        // if successfully updated anything, update the statistic counter
        if ($update === true) $this->utils->seo++;

    }

    protected function compareDescription(string $shopifyDescription, string $koronaDescription) : bool
    {

        // htmlspecialchars_decode needed, because shopify saves & as &amp;
        $shopifyDescription = htmlspecialchars_decode($shopifyDescription);
        $koronaDescription  = htmlspecialchars_decode($koronaDescription);

        // there might be also differences in line breaks
        $shopifyDescription = str_replace("\n", '', $shopifyDescription);
        $koronaDescription  = str_replace("\n", '', $koronaDescription);

        // return the result
        return trim($shopifyDescription) === trim($koronaDescription);

    }

}