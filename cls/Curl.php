<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.2.1
 * @create date: 2022-01-07
 * @modify date: 2022-03-23
 * @description: Sends all requests to the Api of Korona and/or Shopify and returns the result
 *               Note: To get pagination information from Shopify Api the options to return header
 *                     information must be turned on. This results in breaking the easy access to
 *                     the body why the header information will be removed after the pagination info
 *                     was read from it.
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class Curl
{

    // utils object
    protected $utils = null;

    // debugging
    //protected $debug = true;
    protected $debug = false; //original

    // debug requests only
    protected $debugAll = true;

    // extended logs also all curl requests and responses (creates a lot of data)
    protected $extendedDebug = true;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function curlExec(object $config, string $method, string $body, string $resource)
    {

        // init vars
        $apiPassword = ''; $basicAuth = ''; $pageInfo = '';

        // set the api name
        $apiName = $config->get('apiName');

        // set the api url
        $apiUrl = $config->get('apiUrl');

        // log debug info
        $this->logDebug('preparing curl request to: ' . $apiUrl . $resource, null);

        // create options array for curl request
        // todo: replace REST requests mit GRAPHQL requests to get rid of the pagination info
        // in the header and turn it also off for shopify
        // note: https://snippets.webaware.com.au/howto/stop-turning-off-curlopt_ssl_verifypeer-and-fix-your-php-config/
        $options = array (
            CURLOPT_HEADER => $apiName === 'shopify' ? true : false,
            CURLOPT_NOSIGNAL => true,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_TIMEOUT => 45,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_CUSTOMREQUEST => "$method",
            CURLOPT_POSTFIELDS => $body
        );

        // add api depending headers
        if ($apiName === 'shopify' || $apiName === 'shopifyTest') {

            // wait a moment to not exceed the api limit (REST 40 requests per minute and/or 2 requests per second)
            // https://shopify.dev/api/usage/rate-limits
            @usleep(500000);
            
            // update shopify curls
            $this->utils->shopifyCurls++;

            // get the api password
            $apiPassword = $config->get('apiPassword');
    
            // set the header
            $options += array (
                CURLOPT_HTTPHEADER => array (
                    "Content-Type: application/json",
                    "X-Shopify-Access-Token: $apiPassword"
                )
            );
            // $options[CURLOPT_HTTPHEADER][] = "X-Shopify-Access-Token: $apiPassword";

        } else {

            // update korona curls
            $this->utils->koronaCurls++;

            // get the basic authorization hash
            $basicAuth = base64_encode($config->get('apiUsername') . ':' . $config->get('apiPassword'));
    
            // getting an image?
            if (strpos($resource, 'images') > -1) {

                // set the header for image request
                $options += array (
                    CURLOPT_HTTPHEADER => array (
                        "Accept: image/*",
                        "Authorization: Basic $basicAuth"
                    )
                );

            } else {

                // set the header for json request
                $options += array (
                    CURLOPT_HTTPHEADER => array (
                        "Accept: application/json",
                        "Content-Type: application/json",
                        "Authorization: Basic $basicAuth"
                    )
                );
                
            }
            
        }

        // extended debugging enabled?
        if ($this->debug === true && $this->debugAll === true && $this->extendedDebug === true) {

            // start output buffering

            // save the curl output to a file
            $fileHandle = fopen('php://temp', 'w+');

            // extend options array
            $options += array(
                CURLOPT_VERBOSE => true,
                CURLOPT_STDERR => $fileHandle
            );

        }
        
        // clone the options array
        $logOptions = $options;

        // replace sensitive data from the options array
        // use a loop since the place in the array may change after code changes
        for ($i=0; $i < count($logOptions[CURLOPT_HTTPHEADER]); $i++) {

            // shopify authentication
            $logOptions[CURLOPT_HTTPHEADER][$i] = str_replace($apiPassword, '{API PASSWORD}', $logOptions[CURLOPT_HTTPHEADER][$i]);

            // korona authentication
            $logOptions[CURLOPT_HTTPHEADER][$i] = str_replace($basicAuth, '{BASIC AUTH HASH}', $logOptions[CURLOPT_HTTPHEADER][$i]);

        }
        
        // log options
        $this->logDebug('using the following options:', $logOptions);

        try {

            // init curl
            $curl = curl_init($apiUrl . $resource);

            // log that we passed the curl_init function
            $this->logDebug('curl_init - ok');
            
            // set the options
            curl_setopt_array($curl, $options);
            
            // log that we passed the curl_setop_array function
            $this->logDebug('curl_setopt_array - ok');

            // run the curl and get the returned response
            $response = curl_exec($curl);
            
                   
            

            // log that we passed the curl_exec function
            if ($response === false) {

                // log error
                $this->logDebug('curl_exec - failed');

            } else {

                // log success
                $this->logDebug('curl_exec - ok');

            }            

            // extended debugging enabled?
            if ($this->debug === true && $this->debugAll === true && $this->extendedDebug === true) {

                // set the pointer at the beginning of the file
                rewind($fileHandle);

                // get the output data
                $logData = stream_get_contents($fileHandle);

                // remove authentication data
                $logData = str_replace($apiPassword, '{API PASSWORD}', $logData);
                $logData = str_replace($basicAuth, '{BASIC AUTH HASH}', $logData);
                
                // write it into the log file
                $this->logDebug('curl verbose data:', $logData);

            }

            // try to decode the response
            $resp = json_decode($response, JSON_PRETTY_PRINT);

            // debug all?
            if ($this->debugAll === true) {

                // no valid json found?
                if ( ! $resp) {

                    // log the string data
                    $this->logDebug('server response:', $response);

                } else {

                    // log the json data
                    $this->logDebug('server response:', $resp);

                }
                
            }
                                    
            // get error data
            $curlError = curl_error($curl);

            // log curl error
            if ($curlError) $this->utils->logError('curl error in ' . $apiName . ' api (' . $resource . '):', $curlError, true);

            // get the curl info from the request
            $curlRequestInfo = curl_getinfo($curl);

            // extended debugging enabled?
            if ($this->debug === true && $this->debugAll === true && $this->extendedDebug === true) {

                // log curl info
                $this->logDebug('curl request info:', $curlRequestInfo);

            }

            // close the connection
            curl_close($curl);

            // bad request?
            if ($response === 'Bad Request') {

				// log the error
				$this->utils->logError('server response: ' . $response, $curlRequestInfo, true);

            }

            // error?
            if ($response === false) {

				// log the error
				$this->utils->logError('server response: ' . $response, $curlRequestInfo, true);

            }

            // get the status code
            $httpCode = $curlRequestInfo['http_code'];

            // bad request?
            if ($httpCode === 404) {

                // log the error
				$this->utils->logError('curl http code:', $httpCode, $curlRequestInfo, true);

            // cloudflare error?
            } elseif ($curlRequestInfo['http_code'] === 520) {

                // log the error
				$this->utils->logError('curl http code:', $httpCode, $curlRequestInfo, true);

            }

            // shopify request?
            if ($apiName === 'shopify' || $apiName === 'shopifyTest') {

                // get the link to the next page (if available)
                $pageInfo = $this->getPageInfo($response);

            }

            // remove the header data from the response
            if ($apiName === 'shopify' || $apiName === 'shopifyTest') $this->removeHeader($response);
            
            // log the string data
            if ($this->debugAll === true) $this->logDebug('server response without header:', $response);
            
            // get the data
            $data = @json_decode($response);
            
            // no valid data given?
            if ( ! $data) {

                // return the response
                return $response;

            } else {
            
                // if (gettype($data) === 'array') var_export($data);die('df'); 
                // error returned (shopify api)?
                if (isset($data->errors)) {
                    
                    // log the first error and die
                    $this->utils->logError('Curl error in ' . $apiName . ' api (' . $resource . '):', $data->errors, true);

                }

                // if page info is given add it to the data
                if ($pageInfo !== '') $data->pageInfo = $pageInfo;
                
                 


                // return the json data
                return $data;

            }           

        } catch (Exception $error) {

            // log the error
            // note: add the resource so the admin sees in the notification email the cause of the problem
            $this->utils->logError('curl error in ' . $apiName . ' api (' . $resource . '):', $error->getMessage(), null, die);

        }

    }

    protected function getPageInfo(string $response) : string
    {

        // init vars
        $pageInfo = '';
        
        // put the response into an array
        $lines = explode("\n", $response);
        
        // link line look like:
        // link: <https://gm-shoptest.myshopify.com/admin/api/2022-01/products.json?limit=1&page_info=eyJkaXJlY3Rpb24iOiJwcmV2IiwibGFzdF9pZCI6NzM4MDcyNjQ0ODMxNiwibGFzdF92YWx1ZSI6IlRlc3Rwcm9kdWt0IDIifQ>; rel="previous", <https://gm-shoptest.myshopify.com/admin/api/2022-01/products.json?limit=1&page_info=eyJkaXJlY3Rpb24iOiJuZXh0IiwibGFzdF9pZCI6NzM4MDcyNjQ0ODMxNiwibGFzdF92YWx1ZSI6IlRlc3Rwcm9kdWt0IDIifQ>; rel="next"
        // or
        // link: <https://gm-shoptest.myshopify.com/admin/api/2022-01/products.json?limit=1&page_info=eyJkaXJlY3Rpb24iOiJuZXh0IiwibGFzdF9pZCI6NzM4MDcyNjQ0ODMxNiwibGFzdF92YWx1ZSI6IlRlc3Rwcm9kdWt0IDIifQ>; rel="next"

        // loop through the lines
        foreach ($lines as $line) {
            
            // next page link?
            if (substr(trim($line), 0, 5) === 'link:') {

                // split line into an array
                $links = explode(',', $line);

                // loop through the links
                foreach ($links as $link) {

                    // next link?
                    if (strpos($link, 'next') > -1) {

                        // get the link
                        $pageInfo = $this->getLink($link);

                        // exit the loop
                        break;

                    }
                
                }
                
            }
            
        }

        // return the link
        return $pageInfo;

    }

    protected function getLink(string $line) : string
    {

        // init vars
        $link = '';

        // find the page info
        $start = strpos($line, '&page_info');

        // found?
        if ($start > 0) {

            // get the part from &page_info
            $pageInfo = substr($line, $start);
            
            // find the end of the link
            $end = strrpos($pageInfo, '>');

            // found?
            if ($end > 0) {

                // get the link part only
                $link = substr($pageInfo, 0, $end);
                
            }

        }

        // return the link
        return $link;

    }

    protected function removeHeader(string &$response) : void
    {

        // init vars
        $start = false;

        // put the response into an array
        $lines = explode("\n", $response);

        // reset the response
        $response = '';

        // loop through the lines
        foreach ($lines as $line) {

            // after an empty line we have the response only
            if ($start) $response = $line;

            // empty line?
            if (trim($line) === '') $start = true;

        }

    }

    protected function logDebug(string $text, $data=null) : void
    {

        // no debugging enabled?
        if ($this->debug !== true) return;

        // log the debug data
        $this->utils->logCurl($text, $data);

    }

}

