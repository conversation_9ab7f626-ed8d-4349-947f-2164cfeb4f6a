<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.2.0
 * @create date: 2022-01-31
 * @modify date: 2022-03-02
 * @description: Provides methods to control the lock file
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class LockFile
{

    // utils object
    protected $utils = null;

    // file name for the lock file
    protected $lockFile = '';

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

        // set file for sync locking
        $this->lockFile = dirname(__DIR__) . '/~locked.txt';

    }

    public function createLockfile() : void
    {
        
        // on local host do not create the file
        if ($this->utils->isLocal()) return;

        // create temp file to avoid running script again while it is still running and/or an error ocurred
        if ($this->utils->saveFile($this->lockFile, 'Sync is running and/or error ocurred') === true) {

            // log info
            $this->utils->logInfo('Lockfile created: ' . $this->lockFile);

        } else {

            // increase warning counter
            $this->utils->warnings++;

            // log info
            $this->utils->logInfo('Warning: Lockfile not created: ' . $this->lockFile, null, true);

        }

    }
        
    public function checkLockfile() : bool
    {

        // init vars
        $check = false;

        // lock file exists?
        if (file_exists($this->lockFile)) {

            // log info
            $this->utils->logInfo('Sync called while previous process is still running.');

            // lock file outdated?
            if ($this->isOutdatedLockfile() === true) {

                // delete the lockfile so the script can try next call again
                $this->deleteLockfile();

                // job can be called
                return true;

            }

            // body without error message
            $body = 'Sync job was called while a previous job is still running. Please check your cronjob settings and/or log files.';

            // inform admin
            $this->utils->log->sendAdminEmail('Topmarkt - API-Fehler', $body);

            // job can not be called
            $check = false;

        } else {

            // no lock file exists
            $check = true;

        }

        // return the status
        return $check;

    }

    public function isOutdatedLockfile() : bool
    {

        // init vars
        $outdated = false;
        
        // set outdated time to (seconds x minutes)
        $time = 60 * 30;

        // get the last modification time of the lock file
        $fileMtime = filemtime($this->lockFile);

        // echo "$fileMtime<br>"; echo $time + $fileMtime . "<br>";echo time();

        // older than 60 minutes?
        if ($fileMtime + $time < time()) {

            // file outdated
            return true;

        }else{

            // file not outdated
            return false;

        }

    }

    public function deleteLockfile() : void
    {

        // on local host we have no lock file
        if ($this->utils->isLocal()) return;

        // lock file exists?
        if (file_exists($this->lockFile)) {

            // remove lock file
            if (@unlink($this->lockFile)) {

                // log info
                $this->utils->logInfo('Lockfile deleted: ' . $this->lockFile);

            } else {

                // increase error counter
                $this->utils->errors++;

                // log info
                $this->utils->logInfo('Error: Could not delete lockfile: ' . $this->lockFile);

            }
            
        } else {

            // increase error counter
            $this->utils->errors++;

            // log info
            $this->utils->logInfo('Error: Could not find lockfile: ' . $this->lockFile);

        }        

    }

}