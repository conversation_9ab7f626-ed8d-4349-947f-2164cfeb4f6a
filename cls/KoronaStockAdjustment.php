<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     2.0.1
 * @create date: 2022-01-07
 * @modify date: 2022-03-21
 * @description: <PERSON>les functions around orders in Shopify, in first place it finds new/changed order
 *               for the order synchronization with Korona.
 */

// no direct access
defined('GM_EXEC') or die('Access denied');

class KoronaStockAdjustment
{

    // utils object
    protected $utils = null;

    // order to process
    protected $order = null;

    // type of the stock to book (fulfillments and/or refunds)
    protected $stockType = '';

    // data for booking the stock amount(s)
    protected $stockData = '';

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function addStockAdjustment(object $order, string $type) : void
    {

        // set the order var
        $this->order = $order;

        // reset the stock data var
        $this->stockData = '';

        // update the stock type var
        $this->stockType = $type;

        // fulfillment?
        if ($this->stockType === 'fulfillments') {

            // process fulfillment items
            $this->processFulfillmentItems();

        } else {

            // process refund items
            $this->processRefundItems();

        }
        
        // data given?
        if ($this->stockData !== '') {

            // remove last comma
            $this->stockData = substr($this->stockData, 0, -1);

            // data is an array
            $this->stockData = '[' . $this->stockData . ']';

            // update the order data
            if ($this->utils->shopifyUtils->setOrderData($this->order) !== true) return;

            // create a new stock adjustment in korona
            $this->createStockAdjustment();

        }

    }

    protected function processFulfillmentItems() : void
    {

        // init vars
        $stockData = []; $fulfillments = [];

        // log info
        $this->utils->logInfo('Processing fulfillments for order ' . $this->order->name);

        // get the fulfillment items (use the data object, since this have not the guid but the id only)
        $items = $this->utils->shopifyUtils->getFulfillments($this->order->data->id);

        // set the reason for the stock adjustment item
        $this->order->stockReason = $this->order->name . ' (out)';

        // loop through the items
        foreach ($items as $item) {

            // already processed item?
            if ($this->processedItem($item->id) === true) {
             
                // log info
                $this->utils->logInfo('Fulfillment ' . $item->name . ' was already processed', null, true);

                // skip this item
                continue;

            }

            // loop through the line items
            foreach ($item->line_items as $lineItem) {

                // get the quantity (negative to decrease it in the stock of korona)
                $quantity = $lineItem->quantity * -1;

                // get the article id in korona
                $articleId = $this->utils->koronaUtils->getArticleIdBySku($lineItem->sku);

                // article found?
                if ($articleId !== '') {

                    // add the data to the stock data
                    $this->addArticle($quantity, $articleId, $stockData);

                    // create new fulfillments object
                    $fulfillment = new \stdClass();

                    // add data to the fulfillments object
                    $fulfillment->sku = $lineItem->sku;
                    $fulfillment->title = $lineItem->title;
                    $fulfillment->quantity = $quantity;
                    $fulfillment->productId = $lineItem->product_id;
                    $fulfillment->articleId = $articleId;                

                    // add it to the fulfillments array
                    $fulfillments[] = $fulfillment;
                
                }

            }

            // update the order data
            $this->updateOrderData($item->id, $item->name, $fulfillments);

            // reset fulfillments array
            unset($fulfillments);

            // reinitialize it
            $fulfillments = [];

        }
        
        // create a korona conform stock data item
        $this->createStockData($stockData);
        
    }

    protected function processRefundItems() : void
    {

        // init vars
        $stockData = []; $refunds = [];

        // log info
        $this->utils->logInfo('Processing refunds for order ' . $this->order->name);

        // get the refund items (use the data object, since this have not the guid but the id only)
        $items = $this->utils->shopifyUtils->getRefunds($this->order->data->id);

        // set the reason for the stock adjustment item
        $this->order->stockReason = $this->order->name . ' (in)';

        // loop through the items
        foreach ($items as $item) {

            // already processed item?
            if ($this->processedItem($item->id) === true) {
             
                // log info
                $this->utils->logInfo('Refund ' . $item->id . ' for order ' . $this->order->name . ' was already processed', null, true);

                // skip this item
                continue;

            }
    
            // loop through the line items
            foreach ($item->refund_line_items as $refundLineItem) {

                // get the line item for this refund line item (what nonsense is that?)
                $lineItem = $refundLineItem->line_item;

                // get the quantity
                $quantity = $refundLineItem->quantity;

                // get the article id in korona
                $articleId = $this->utils->koronaUtils->getArticleIdBySku($lineItem->sku);

                // article found?
                if ($articleId !== '') {

                    // add the data to the stock data
                    $this->addArticle($quantity, $articleId, $stockData);

                    // create new refunds object
                    $refund = new \stdClass();

                    // add data to the refunds object
                    $refund->sku = $lineItem->sku;
                    $refund->title = $lineItem->title;
                    $refund->quantity = $quantity;
                    $refund->productId = $lineItem->product_id;
                    $refund->articleId = $articleId;                             

                    // add it to the refunds array
                    $refunds[] = $refund;

                }

            }

            // update the order data
            $this->updateOrderData($item->id, '', $refunds);
            
            // reset refunds array
            unset($refunds);

            // reinitialize it
            $refunds = [];
            
        }
        
        // create a korona conform stock data item
        $this->createStockData($stockData);

    }

    protected function processedItem(string $id) : bool
    {

        // note: since the api is bad programmed, we have names in fulfillments, but not in refunds
        // so use the ids for both only

        // fulfillment?
        if ($this->stockType === 'fulfillments') {
            
            // search in the fulfillments data if this item is already processed
            foreach ($this->order->data->fulfillments as $fulfillment) {

                // found?
                if ($fulfillment->id === $id) return true;

            }

        } else {

            // search in the refund data if this item is already processed
            foreach ($this->order->data->refunds as $refund) {

                // found?
                if ($refund->id === $id) return true;

            }

        }
        
        // not yet processed item
        return false;

    }

    protected function addArticle(int $quantity, string $articleId, array &$stockData) : void
    {

        // check if the article exists already in the data
        // this is necessary because an article can fulfilled / refunded multiple times
        // before the synchronization runs again and korona is not able to process an item
        // with the same quantity twice in the same stock adjustment item.
        
        // search for the article id in the array
        $index = array_search($articleId, array_column($stockData, 'id'));

        // is the article already in the stock data var?
        if ($index === false) {

            // add the article to the stock data var
            $stockData[] = array('amount' => $quantity, 'id' => $articleId);

        } else {

            // add the amount of the article to the stock data var
            $stockData[$index]['amount'] = $stockData[$index]['amount'] + $quantity;

        }
            
    }

    protected function updateOrderData(string $id, string $name, array $orderData) : void
    {

        // note: on refunds we have no name

        // create new object
        $data = new \stdClass();

        // add the needed data to it
        $data->id = $id;        
        $data->name = $name;
        $data->data = $orderData;

        // fulfillment?
        if ($this->stockType === 'fulfillments') {

            // add the name to the fulfillments array
            $this->order->data->fulfillments[] = $data;

        } else {

            // add the name to the refunds array
            $this->order->data->refunds[] = $data;

        }

    }

    protected function createStockData(array $stockData) : void
    {

        // reset the stock data var
        $this->stockData = '';

        // loop through the array
        foreach ($stockData as $stockItem) {

            // put the data for this article together
            // note: the key "comment" for "reason" is not visible in the ui and therefore useless
            $this->stockData .= '
            {
                "amount": ' . $stockItem['amount'] . ',
                "product": {
                    "id": "' . $stockItem['id'] . '"
                }';

            // refund?
            if ($this->stockType === 'refunds') {

                // add a reason
                $this->stockData .= ',"reason": {
                        "type":"OTHER"
                    }';

            }
            
            // close the data
            $this->stockData .= '},';

        }
        
    }

    protected function createStockAdjustment() : void
    {

        // init vars
        $returned = 0;

        // put the body together
        $body = '{
            "warehouse": {
                "id": "' . $this->utils->koronaOrganizationId . '"
            },
            "reason":"' . $this->order->stockReason . '"
        }';

        // create a new stock adjustment item
        $result = $this->utils->curlExec('korona', 'POST', $body, 'stockAdjustments');

        // problem?
        if ( ! isset($result[0]->id)) {

            // log error and die
            $this->utils->logError('Could not create new stock adjustment item', $result, true);

        }
      
        // log info
        // $this->utils->logInfo('New stock adjustment item created', $result);

        // get the id of the new created stock adjustment item
        $id = $result[0]->id;

        // get the number of the new created stock adjustment item
        $number = $result[0]->number;

        // set the data for the article items to the body
        $body = $this->stockData;

        // log info
        $this->utils->logInfo('New stock adjustment data: ' . $this->stockData);

        // create a new stock adjustment
        // note: even in the ui you can simple book a not saved item, the api needs to save it before booking
        $result = $this->utils->curlExec('korona', 'POST', $body, 'stockAdjustments/' . $id . '/items');
        
        // problem?
        if (isset($result->message)) {

            // log the error and die
            $this->utils->logError($result->message, $result, true);

        }

        // log info
        // $this->utils->logInfo('New stock adjustment item saved: ' . $data, $result);

        // book the new stock adjustment
        $result = $this->utils->curlExec('korona', 'PATCH', '', 'stockAdjustments/' . $id . '/book');
        
        // problem?
        if (isset($result->message)) {

            // log the error and die
            $this->utils->logError($result->message, $result, true);

        }

        // increase counter for statistic log
        $this->utils->stockAmountBookings++;

        // log info
        $this->utils->logInfo('New stock adjustment item no. ' . $number . ' booked: ' . $this->order->stockReason, null, true);
        
    }  

}