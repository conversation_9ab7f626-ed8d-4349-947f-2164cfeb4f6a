<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.0
 * @create date: 2022-02-28
 * @modify date: 2022-03-03
 * @description: Updates changed article images in Korona in Shopify
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class KoronaProductUpdateImages
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function updateImages() : void
    {
     
        // init vars
        $maxRevision = 0; $item = 0; $items = 0;

        // get the last revision number
        $revision = $this->utils->getRevision('images');

        // update max revision var for the case no items where changed and so the var would not be updated
        $maxRevision = $revision;

        // fetch changed products for the online shop in korona
        $this->utils->koronaUtils->getKoronaProducts($revision);

        // get all shopify products (needed in ShopifyProducts class)
        $this->utils->shopifyUtils->getShopifyProducts();

        // log info
        $this->utils->logInfo('Fetched Shopify products: ' . count($this->utils->shopifyProducts));
            
        // log info
        $this->utils->logInfo('Fetched Korona products: ' . count($this->utils->koronaProducts));

        // log info
        $this->utils->logInfo('Current images revision: ' . $revision);

        // get the number of product items
        $items = count($this->utils->koronaProducts);

        // loop through the products
        foreach ($this->utils->koronaProducts as $koronaProduct) {

            // increase item counter
            $item++;

            // set sync type for shopify product class
            $koronaProduct->sync = 'image';

            // log progress
            $this->utils->logInfo('Processing article #' . $koronaProduct->number . ' (' . $item . '/' . $items . ')');

            // skip inactive products
            if ($koronaProduct->deactivated === true) {
                
                // log info
                $this->utils->logInfo('Skipped deactivated item');

                // process next item
                continue;

            }

            // revision number higher then the current?
            if (intval($koronaProduct->revision) > $maxRevision) {

                // set new max value
                $maxRevision = intval($koronaProduct->revision);

            }

            // revision number lower then the current?
            // note: this check is needed if a previous job was called and fetched all korona articles
            if (intval($koronaProduct->revision) < $revision) {

                // log info
                $this->utils->logInfo('Skipped not changed item');

                // process next item
                continue;

            }

            // check if product image(s) has changed
            $this->checkImages($koronaProduct);
                
        }

        // set new max revision only if update was not forced, since it is 0 in this case
        if ($this->utils->get('korona', 'forceUpdate') !== true) {

            // log info
            $this->utils->logInfo('New images revision: ' . $maxRevision);

            // save the revision for the next request (after updating/creating products)
            $this->utils->setRevision('images', $maxRevision);

        }

    }

    protected function checkImages(object $koronaProduct) : void
    {

        // set seo data to get seo friendly alt attribute
        $this->utils->koronaUtils->setSeo($koronaProduct);

        // use images object
        $class = new Images($this->utils);

        // save the image(s) in the image directory (this will create a hash file in the directory too)
        $class->setImages($koronaProduct);

        // call the check product method, which will search for the shopify product and call the update images method
        $this->utils->shopifyUtils->checkProduct($koronaProduct);

    }

}