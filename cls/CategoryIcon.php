<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.0.1
 * @create date: 2022-01-07
 * @modify date: 2022-02-18
 * @description: Returns an emoji icon to a category name
 *               Icons see http://www.amp-what.com/ and https://emojidb.org/
 *               Note: If you only see blank squares instead of icons you have to install a font
 *                     which is able to display the icons.
 *
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class CategoryIcon
{

    // utils object will be created in the init.php file
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function getCategoryIcon(string $category) : string
    {

        // call the method and return the icon
        return $this->returnCategoryIcon($category);

    }

    protected function returnCategoryIcon(string $category) : string
    {

        // init vars
        $icon = '';

        // choose the icon
        switch ($category) {

            case 'Bäckerei':

                $icon = '🥞';

                break;

            case 'Backzutaten':

                $icon = '🥞';

                break;

            case 'Babynahrung & Kindermilch':
            case 'Babynahrung & Kinder Milch':
                
                $icon = '🍼';

                break;

            case 'Bier':
            
                $icon = '🍺';

                break;
                
            case 'Blätterteigwaren':

                $icon = '🥐';

                break;

            case 'Brot & Backwaren':
                
                $icon = '🍞';

                break;

            case 'Drogerie & Kosmetik':
                
                $icon = '💄';

                break;

            case 'Einkaufstasche':
                
                $icon = '🛍️';

                break;

            case 'Eis':
            
                $icon = '🍨';

                break;
                
            case 'Elektroartikel':
                
                $icon = '⚡';

                break;

            case 'Feta':
            
                $icon = '🧀';

                break;

            case 'Fertiggerichte':

                $icon = '🥫';

                break;
                
            case 'Fertiggerichte & Konserven':

                $icon = '🥫';

                break;

            case 'Fisch':
            case 'Fisch FK':
            case 'Fisch TK':

                $icon = '🐟';

                break;               
            
            case 'Fleisch':

                $icon = '🥩';

                break;
                
            case 'Gebäck & Kekse':

                $icon = '🍪';

                break;
            
            case 'Gemüse':
                
                $icon = '🥕';

                break;

            case 'Gewürze & Essig':

                $icon = '🥗';

                break;
                
            case 'Gutscheine':
                
                $icon = '🎟️';

                break;

            case 'Haus & Freizeit':
                
                $icon = '🏡';

                break;

            case 'Haushaltsartikel':

                $icon = '💡';

                break;
                
            case 'Honig & Tahini & Halva':
                
                $icon = '🍯';

                break;

            case 'Hülsenfrüchte & Reis':
            case 'Hülsenfrüchte und Reis':

                $icon = '🍚';

                break;

            case 'Insektizide':

                $icon = '🦟';

                break;
            
            case 'Kaffee & Milch':
                
                $icon = '☕';

                break;

            case 'Käse':
        
                $icon = '🧀';

                break;
                
            case 'Kirchliche Produkte':

                $icon = '⛪';

                break;

            case 'Konfitüren & Loukoum':

                $icon = '🍓';

                break;

            case 'Küche & Haushalt':
                
                $icon = '🔪';

                break;

            case 'Kühltheke':

                $icon = '🌨️';

                break;

            case 'Liköre & Metaxa':
                
                $icon = '🥃';

                break;
                
            case 'Margarine & Butter':

                $icon = '🧈';

                break;
                
            case 'Milchprodukte':

                $icon = '🥛';

                break;

            case 'Nahrungsmittel':
                
                $icon = '🍴';

                break;

            case 'Non Food':
            
                $icon = '💡';

                break;

            case 'Nüsse & Pasteli & Cerealien':

                $icon = '🥜';

                break;

            case 'Öl & Oliven':
                
                $icon = '🫒';

                break;

            case 'Obst & Gemüse':

                $icon = '🥑';

                break;
            
            case 'Ouzo & Tsipouro':
                
                $icon = '🥃';

                break;

            case 'Pasta & Nudeln':

                $icon = '🍝';

                break;

            case 'Pita & Brot':
                
                $icon = '🍞';

                break;

            case 'Pfand':
                
                $icon = '♻️';

                break;

            case 'Putzen & Reinigen':

                $icon = '🧹';

                break;
                
            case 'Retsina':

                $icon = '🍾';

                break;

            case 'Rosewein':

                $icon = '🍷';

                break;
                
            case 'Rotwein':

                $icon = '🍷';

                break;
                
            case 'Säfte & Getränke':

                $icon = '🧃';
    
                break;

            case 'Saisonale Produkte':
            case 'Saisonale Produkte NF':
            
                $icon = '⛅';

                break;
    
            case 'Salate':
                
                $icon = '🥗';
    
                break;

            case 'Saucen & Pasten':

                $icon = '🍝';
    
                break;

            case 'Schokolade & Süssigkeiten':

                $icon = '🍫';

                break;
                
            case 'Snacks & Croissants':

                $icon = '🥐';

                break;
            
            case 'Stevia Produkte & Zuckerfrei':
            case 'Stevia Produkte & Zucker frei':

                $icon = '🍭';

                break;

            case 'Tee':

                $icon = '🫖';

                break;
                
            case 'Tiefkühltheke':

                $icon = '⛄';

                break;

            case 'Vegan':
            case 'Vegan FK':
            case 'Vegan TK':
            
                $icon = '🥗';
    
                break;
                
            case 'Waschen & Bügeln':

                $icon = '🚿';

                break;
                
            case 'Wein & Spirituosen':

                $icon = '🍷';

                break;
            
            case 'Weisswein':

                $icon = '🍾';

                break;
                
            case 'Wurst & Fleisch':

                $icon = '🥩';

                break;
            
            default:

                $icon = '🛒';

                // increase warning counter
                $this->utils->warnings++;

                // log info
                $this->utils->logInfo('Warning: No icon for group ' . $category . ' found - used default icon instead ' . $icon, null, true);

                break;

        }

        // if icon was found add a space
        if ($icon !== '') $icon .= ' ';

        // return the icon
        return $icon;

    }

}