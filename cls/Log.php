<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     2.3.2
 * @create date: 2022-01-07
 * @modify date: 2022-03-23
 * @description: <PERSON><PERSON> writing and reading log data, archiving old log files and sending log reports
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

// set correct time zone
date_default_timezone_set('Europe/Berlin');

class Log
{

    // utils object
    protected $utils = null;

    // enable / disable logging
    protected $logging = true;

    // path to the date directory
    protected $datePath = '';

    // logs directory
    protected $logsDir = '';

    // daily log directory
    protected $logDateDir = '';

    // path for the log file
    protected $logFile = '';

    // path for the curl log file (same as before but in a subdirectory)
    protected $curlLogFile = '';

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

        // set date path
        $this->datePath = date('Y') . '/' . date('m') . '/' . date('d') . '/';

        // path to the logs directory
        $this->logsDir = dirname(__DIR__) . '/logs/';

        // path to log directory
        $this->logDateDir = $this->logsDir . $this->datePath;

        // set log file paths
        $this->logFile     = $this->getLogFile('info');
        $this->curlLogFile = $this->getLogFile('curl');
        
    }

    public function createDirectories() : void
    {
            
        // make sure the path to the daily log exists
        @mkdir($this->logDateDir, 0700, true);

        // make sure the path to the curl directory exists
        // only when needed, see logData function
        // @mkdir($this->logDateDir . 'curl/', 0700, true);

    }

    protected function getLogFile($type='info') : string
    {

        // get the next file number
        $fileNumber = $this->getFileNumber($type);

        // for curl make an own directory to not mess up this big data with the normal log data
        if ($type === 'curl') {

            // use curl log subdirectory
            $logFile = $this->logDateDir . 'curl/' . $fileNumber . '.log';

        } else {

            // use normal log directory
            $logFile = $this->logDateDir . $fileNumber . '.log';  

        }

        // return the log filename
        return $logFile;

    }

    protected function getFileNumber(string $type) : string
    {

        // init vars
        $fileNumber = 0;

        // use type in front of the file name for curl log data
        if ($type === 'curl') {
            
            // get the number of log files in the log curl sub directory
            $files = glob($this->logDateDir . 'curl/*.log');

        } else {

            // get the number of log files in the log directory
            $files = glob($this->logDateDir . '*.log');

        }

        // files found?
        if ($files !== false) {

            // get the number of found files
            $fileNumber = count($files);

        }

        // increase the file number
        $fileNumber++;

        // file number is always 4 digits
        // if a job runs every 5 minutes we have a maximum file number of 288
        $fileNumber = str_pad($fileNumber, 4, '0', STR_PAD_LEFT);

        // return the file number
        return $fileNumber;

    }

    public function logData(string $type, string $text, $data=null, $die=false) : void
    {

        // init vars
        $temp = '';

        // get log file 
        $logFile = $this->getLogFileName($type);

        // data given?
        if ( ! is_null($data)) {

            // add the data to the temp var
            $temp = var_export($data, true);
            
            // add data to the text var
            $text .= "\n" . $temp;

        }

        // add date in front of the text
        $text = (new \DateTime())->format('Y-m-d G:i:s:v') . ' ' . $text;
        
        // add 2 line breaks
        $text .= "\n\n";

        // logging enabled?
        if ($this->logging === true) {

            // use cache logging?
            if ($this->utils->get('korona', 'cacheLogging') === true && $type !== 'curl') {

                // add the info to the cache var
                $this->utils->logCache .= $text;

            } else {
                
                // only create the curl directory, if needed, otherwise the clean function will 
                // send delete report on empty deleted log directories
                if ($type === 'curl') {

                    // make sure the path to the curl directory exists
                    @mkdir($this->logDateDir . 'curl/', 0700, true);

                }
                    
                // write text into the log file
                $this->utils->saveFile($logFile, $text, true);

            }
                        
        }       
        
        // error?
        if ($type === 'error') {

            // increase error counter
            $this->utils->errors++;

            // finished?
            if ($die === true) {

                // inform admin via email about the error
                $this->sendAdminEmail('Topmarkt - API-Fehler', $text);

            }

        }

        // stop further program execution?
        if ($die === true) {

            // if log caching is enabled, write the log cache to the disk
            $this->flushLogCache('');
            
            // temporary shopify server error and/or lock file to old?
            if ($this->isTempError($text) === true) {
            
                // delete the lockfile so the script can try next call again
                $this->utils->lockFile->deleteLockfile();

            }

            // make sure file has correct permissions
            @chmod($logFile, 0600);

            // show the error in the frontend
            $this->utils->outputResult();

        }

    }

    protected function isTempError(string $error) : bool
    {

        // init vars
        $temp = false;

        // connection timeout error on korona/shopify server?
        if (strpos($error, 'Resolving timed out') > -1) $temp = true;

        // timeout error on korona/shopify server?
        if (strpos($error, 'Operation timed out') > -1) $temp = true;

        // curl error on shopify server?
        if (strpos($error, 'Internal Server Error') > -1) $temp = true;

        // read problem?
        if (strpos($error, 'Can not read ') > -1) $temp = true;

        // return the result
        return $temp;

    }

    public function addLogData() : string
    {

        // init vars
        $logData = '';

        // add info log
        $logData .= $this->getLogData();

        // return the log data
        return $logData;

    }

    protected function getLogData() : string
    {

        // init vars
        $logData = '';

        // try to read the log
        $content = $this->readLog();

        // found?
        if ($content !== '') {

            // split it into an array
            $lines = explode("\n", $content);

            // loop through the lines
            foreach ($lines as $line) {

                // add it to the var
                $logData .= '<p>' . $line . '</p>';

            }

        }

        // return the data
        return $logData;

    }

    protected function flushLogCache(string $info='') : void
    {

        // if log caching is enabled, write the log cache to the disk
        if ($this->utils->get('korona', 'cacheLogging') === true) {

            // set content var
            $content = $this->utils->logCache;

            // if info is given, add it in front of it
            if ($info !== '') $content = $info . $content;

            // put text into the log file
            $this->utils->saveFile($this->logFile, $content);

        }

    }

    public function sendAdminEmail(string $subject, string $body) : void
    {

        // get admin email address
        $adminEmail = $this->utils->get('korona', 'adminEmail');

        // no admin email address given?
        if (strpos($adminEmail, '@') === false) return;

        // mail function needs , to separate email addresses
        $adminEmail = str_replace(';', ',', $adminEmail);

        // get from address
        $from = $this->getFromAddress($adminEmail);

        // set header
        $from = 'From: API Service <' . $from . '>' . "\r\n" . 'X-Mailer: PHP / ' . phpversion();

        // send the email
        mail($adminEmail, $subject, $body, $from);

    }

    public function sendResult() : void
    {

        // init vars
        $body = ''; $send = false;

        // get email recipients
        $to = $this->utils->get('korona', 'reportRecipients');

        // mail function needs , to separate email addresses
        $to = str_replace(';', ',', $to);

        // remove spaces if somebody entered which
        $to = str_replace(' ', '', $to);

        // get from address
        $from = $this->getFromAddress($to);

        // get the job human readable
        switch ($this->utils->job) {

            case 'syncData': default:

                $jobName = 'Data';

                break;

            case 'updateImages':

                $jobName = 'Images';

                break;

        }

        // set the subject
        $subject = 'Topmarkt - ' . $jobName . ' synchronization finished';

        // force sending the email?
        $force = $this->utils->get('korona', 'forceSendReport') ? 'Yes' : 'No';

        // put statistic in front of the log file without timestamps
        $info = $this->logInfo($to, $force);

        // if log caching is enabled, write the info log cache to the disk
        $this->flushLogCache($info);

        // do not send the report?
        // must be done after the info was flushed
        if ($this->utils->get('korona', 'sendReport') === false) return;

        // use only short info log?
        if ($this->utils->get('korona', 'shortLog') === true) {

            // add only the short log
            $body .= $this->utils->shortLog . $info;

        } else {

            // add complete info log
            $body .= $this->readLog();

        }

        // something changed and/or force sending the report?
        $send = $this->utils->collectionsCreated > 0 ||
                $this->utils->collectionsUpdated > 0 ||
                $this->utils->errors             > 0 || 
                $this->utils->created            > 0 || 
                $this->utils->prices             > 0 || 
                $this->utils->stocks             > 0 ||                
                $this->utils->imagesDeleted      > 0 || 
                $this->utils->imagesCreated      > 0 || 
                $this->utils->imagesUpdated      > 0 || 
                $force === 'Yes';

        // send the email?
        if ($send === true) {

            // no recipients -> no email
            if (strpos($to, '@') < 0) return;
            
            // log info
            $this->utils->logInfo('Send email to:' . $to);

            // log info
            $this->utils->logInfo('Send email from:' . $from);

            // set header
            $from = 'From: API Service <' . $from . '>' . "\r\n" . 'X-Mailer: PHP / ' . phpversion();

            // send the email
            if (mail($to, $subject, $body, $from)) {

                // log info
                $this->utils->logInfo('Mail sent to:' . $to);

            } else {

                // log info
                $this->utils->logInfo('Error: Mail NOT sent to:' . $to);

            }

        }
        
    }

    protected function getFromAddress(string $to) : string
    {

        // init vars
        $from = '';

        // multiple recipients?
        if (strpos($to, ',') > -1) {

            // split up the recipients
            $recipients = explode(',', $to);

            // use first to address as from address
            $from = $recipients[0];

        } else {

            // use the to address as from address
            $from = $to;

        }            

        // return the from address
        return $from;

    }

    protected function logInfo(string $to, string $force) : string
    {

        // init vars
        $info = ''; $ip = '';

        // get the runtime in human readable format
        $runTime = $this->getTime($this->utils->runTime);

        // get the maximum execution time in human readable format
        $maxTime = $this->getTime(ini_get('max_execution_time'));
               
        // get total number of curl requests
        $curls = $this->utils->koronaCurls + $this->utils->shopifyCurls;

        // when forcing an update do not use a revision number to get all items, not only the changed
        $forceUpdate = $this->utils->get('korona', 'forceUpdate') === true ? 'Yes' : 'No';

        // stock amounts forces update
        if ($this->utils->job === 'updateStocks') $forceUpdate = 'Yes';

        // create products in shopify only with images
        $images = $this->utils->get('shopify', 'needImage') === true ? 'Yes' : 'No';

        // update titles and/or descriptions too?
        $updateAll = $this->utils->get('shopify', 'updateAll') === true ? 'Yes' : 'No';

        // get stock buffer
        $stockBuffer = intval($this->utils->get('shopify', 'stockBuffer'));

        // get the servers ip address
        $ip = $this->utils->getServerIp();

        // if no local ip is given, add a look up service
        if ($ip !== '' && $ip !== '127.0.0.1') $ip = 'https://ipinfo.io/' . $ip;
        
        // data sync called?
        if ($this->utils->job === 'syncData' || $this->utils->job === 'all') {

            // create data sync info
            $info .= 'Shopify orders booked in Korona: ' . $this->utils->stockAmountBookings . "\n";
            $info .= 'Updated stock amounts in Shopify: ' . $this->utils->stocks . "\n";
            $info .= 'Updated prices in Shopify: ' . $this->utils->prices . "\n";            
            $info .= 'Updated titles/descriptions in Shopify: ' . $this->utils->seo . "\n";        
            $info .= 'Created products in Shopify: ' . $this->utils->created . "\n";
            $info .= 'Products for Shopify in Korona: ' . count($this->utils->koronaProducts) . "\n";
            $info .= 'Products in Shopify: ' . count($this->utils->shopifyProducts) . "\n";        
            $info .= 'Inventory items in Shopify: ' . count($this->utils->shopifyInventoryItems) . "\n";
            $info .= 'Created categories in Shopify: ' . $this->utils->collectionsCreated . "\n";
            $info .= 'Updated categories in Shopify: ' . $this->utils->collectionsUpdated . "\n\n";
            
        }
        
        if ($this->utils->job === 'updateStocks' || $this->utils->job === 'all') {

            // stock amount data needed
            $info .= 'Updated stock amounts in Shopify: ' . $this->utils->stocks . "\n";
            $info .= 'Product stock items in Korona: ' . count($this->utils->koronaProductStocks) . "\n";
            $info .= 'Inventory items in Shopify: ' . count($this->utils->shopifyInventoryItems) . "\n";
            $info .= 'Products for Shopify in Korona: ' . count($this->utils->koronaProducts) . "\n";
            $info .= 'Products in Shopify: ' . count($this->utils->shopifyProducts) . "\n\n";        

        }

        if ($this->utils->job === 'updateImages' || $this->utils->job === 'all') {

            // create update images info
            // $info .= 'Deleted images in Shopify: ' . $this->utils->imagesDeleted . "\n";
            $info .= 'Updated images in Shopify: ' . $this->utils->imagesUpdated . "\n";
            $info .= 'Created images in Shopify: ' . $this->utils->imagesCreated . "\n\n";        

        }
        
        // add statistic
        $info .= 'Requests to Korona: ' . $this->utils->koronaCurls . "\n";
        $info .= 'Requests to Shopify: ' . $this->utils->shopifyCurls . "\n";
        $info .= 'Requests total: ' . $curls . "\n";
        $info .= 'Warnings: ' . $this->utils->warnings . "\n";
        $info .= 'Errors: ' . $this->utils->errors . "\n";
        $info .= 'Duration: ' . $runTime . "\n";
        $info .= 'Time limit: ' . $maxTime . "\n\n";

        // add settings
        $info .= 'Force update even on not changed items: ' . $forceUpdate . "\n";
        $info .= 'Send report even if nothing changed: ' . $force . "\n";
        $info .= 'Create products in Shopify only with images: ' . $images . "\n";

        // data sync called?
        if ($this->utils->job === 'syncData' || $this->utils->job === 'all') {

            // add data sync settings
            $info .= 'Stock buffer: ' . $stockBuffer . "\n";            
            $info .= 'Update titles and/or descriptions: ' . $updateAll . "\n";

        }

        // add other stuff
        $info .= 'Recipients for result email: ' . $to . "\n";
        $info .= 'Service called: ' . $this->utils->callType . "\n";
        if ($ip) $info .= 'Server: ' . $ip . "\n";
        $info .= 'App version: ' . $this->utils->appVersion . "\n";
        $info .= 'PHP version: ' . PHP_VERSION . "\n";
        $info .= 'OS version: ' . php_uname('v') . "\n";
        $info .= 'Logfile: /logs/' . $this->datePath . basename($this->logFile) . "\n\n";

        // read the content
        $content = $this->readLog();

        // put the info at the beginning of the log file
        $content = $info . $content;

        // save it
        $this->utils->saveFile($this->logFile, $content);

        // return it for a short log result email
        return $info;

    }

    public function readLog() : string
    {

        // init vars
        $content = '';

        // get the log file
        $logFile = $this->getLogFileName('info');

        // found?
        if (file_exists($logFile)) {

            // read the content
            $content = file_get_contents($logFile);

            // problem?
            if ($content === false) $content = '';

        }

        // return the content
        return $content;

    }

    protected function getLogFileName($type) : string
    {

        // init vars
        $filename = '';

        // get the log filename
        switch ($type) {

            // use one file for info and errors so it is easier to read it
            case 'info': case 'error': default:

                // use the info file
                $filename = $this->logFile;

                break;

            case 'curl':

                // use the curl file
                $filename = $this->curlLogFile;

                break;

        }

        // return the filename
        return $filename;

    }

    public function cleanLogs() : void
    {

        // cleans class needed
        $class = new CleanLogs($this->utils);

        // call the clean method
        $result = $class->cleanDirectory($this->logsDir);

        // something deleted?
        if ($result !== '') {

            // inform admin
            $this->sendAdminEmail('Logfiles deleted', $result);

        }

        // if nothing is deleted show an information message
        if ($result === '') $result = 'Nothing to delete today.';

        // output the result
        $this->utils->outputResult($result);

    }

    protected function getTime(float $time) : string
    {

        // calculate the runtime
        if ($time > 60) {

            // get time in minutes
            $timeString = number_format($time / 60, 2, ',', '.') . ' minutes';

        } else {

            // get time in seconds
            $timeString = number_format($time, 2, ',', '.') . ' seconds';

        }

        // return the time string
        return $timeString;

    }

}