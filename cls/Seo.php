<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     2.1.1
 * @create date: 2022-01-07
 * @modify date: 2022-03-09
 * @description: Makes the product name better readable, gets the weight information from the title
 *               and generates the product description, the seo title and the seo description.
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class Seo
{

    // utils object
    protected $utils = null;

    // category icon object
    protected $categoryIcon = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function humanizeProductName(object $koronaProduct) : void
    {

        // init vars
        $weight = 0.0;

        // humanize the product name and get the weight
        $koronaProduct->name = $this->humanizeText($koronaProduct->name, $weight);

        // set the grams
        $koronaProduct->weight = $weight;

        // humanize the product description
        // do not make this, since descriptions are not yet available and the
        // default description will have to many uppercase characters
        // $koronaProduct->description = $this->humanizeText($koronaProduct->description);

    }

    protected function humanizeText(string $text, int &$weight=0) : string
    {

        // init vars
        $firstWord = true; $weight = 0.0; $index = 0;
// $this->utils->outputResult($text);       

// todo: 17Waschungen

        // correct characters
        $this->correctSign($text, '+');
        $this->correctSign($text, '&');
        $this->correctDots($text);
        $this->correctPercent($text);
        $this->correctBrackets($text);
        $this->correctSlashes($text);

        // correct grams for better weight detection
        $this->correctGrams($text);
                
        // get the lower case words
        $content = file_get_contents($this->utils->dataSeoDir . 'lowerCase.txt');

        // put these words always in lower case
        $lowerCase = explode("\n", $content);
// $this->utils->outputResult($text);                    

        // camel case the name
        // note: uc functions does not work with umlauts etc.
        // $text = ucwords(strtolower($text), ' -');
        $text = mb_convert_case(mb_strtolower($text), MB_CASE_TITLE, 'UTF-8');

        // remove unnecessary data
        $text = str_replace('.No8104', '', $text);
        
// $this->utils->outputResult($text);
        
        // correct x (after mb_convert_case since it makes X from x)
        $this->correctX($text);
        
        // split the words into an array
        $words = mb_split("\s", $text);

        // reset the text var
        $text = '';

        // replace lowercase words
        foreach ($words as $word) {
// echo "Word:$word<br>";
            // first word?
            if ($firstWord === true) {

                // reset var
                $firstWord = false;

                // first word is needed unchanged
                $text .= $word . ' ';

                // process next word
                continue;

            }

            // correct numbers
            $this->correctNumbers($word);
            
            // lower case word found?
            if (in_array(str_replace('(', '', $word), $lowerCase)) {

                // rebuild the text with a lower case word
                $text .= mb_strtolower($word, 'UTF-8') . ' ';

            } else {

                // get the weight information from the word
                // before the word is added since it may change the word (unit)
                $this->getWeight($words, $word, $index, $weight);

                // rebuild the text with the unchanged word
                $text .= $word . ' ';

            }

            // increase index counter
            $index++;

        }
        // var_export($text);die('Gewicht:'.$weight);die();
        // no valid weight found?
        if ( ! is_numeric($weight)) $weight = 0;

        // remove spaces
        $text = trim($text);
        
        // before calling the multiply method, make sure no double spaces are given in the text var
        $this->utils->removeDoubleSpaces($text);

        // if weight was found check for multiplying it
        if ($weight > 0) $this->multiplyWeight($text, $weight);

        // change units
        $this->replacePiece($text);
        // $this->replaceUnits($text, 'Lt', ' Liter');
        // $this->replaceUnits($text, 'Ml', ' ml');
        // $this->replaceUnits($text, 'Kg', 'kg');
        // $this->replaceUnits($text, 'Gr', 'g');

        // change producer name
        $this->correctNames($text);
        
        // again make sure no double space are left in the text var
        $this->utils->removeDoubleSpaces($text);

        // correct special stuff (gatis comes from replacing Gr with g)
        $text = str_replace('1gratis', '1 gratis', $text);
        $text = str_replace('2gratis', '2 gratis', $text);
        $text = str_replace('3gratis', '3 gratis', $text);
        $text = str_replace('g. ', 'g ', $text);
        if (substr($text, -2) === 'g.') $text = substr($text, 0, -1);
        $text = str_replace('Anise ', 'Anis ', $text);
        $text = str_replace('Babykreme', 'Babycreme', $text);
        $text = str_replace('duenn', 'dünn', $text);
        $text = str_replace('1.5', '1,5', $text);
        $text = str_replace('Bonbonsjelly', 'Bonbons Jelly', $text);
        $text = str_replace('Doppelgefuellte', 'doppeltgefüllte', $text);
        $text = str_replace('Fruchtkreme', 'Fruchtcreme', $text);
        $text = str_replace('Götter Speise', 'Götterspeise', $text);        
        $text = str_replace('in Vaze', 'im Glas', $text);
        $text = str_replace('in Vase', 'im Glas', $text);
        $text = str_replace('im Vaze', 'im Glas', $text);
        $text = str_replace('im Vase', 'im Glas', $text);
        $text = str_replace('Ion Nucrema in Vase', 'Ion Nucrema in Plastik', $text);
        $text = str_replace('in Plastik Vase', 'in Plastikbehälter', $text);
        $text = str_replace('Kakaotuete', 'Kakaotüte', $text);
        $text = str_replace('Karamell-Creme', 'Karamellcreme', $text);
        $text = str_replace('Karamelliesiert', 'Karamellisiert', $text);
        $text = str_replace('Kreme', 'Creme', $text);
        $text = str_replace('Kraeuter', 'Kräuter', $text);
        $text = str_replace('Maisstaerke', 'Maisstärke', $text);
        $text = str_replace('Olivenoel', 'Olivenöl', $text);
        $text = str_replace('Schwarzaeugige', 'schwarzäugige', $text);        
        $text = str_replace('Vanillekreme', 'Vanillecreme', $text);
        $text = str_replace('Wein weiss', 'Weißwein', $text);
        $text = str_replace('Wein rot', 'Rotwein', $text);
        $text = str_replace('Zahnstoecher', 'Zahnstocher', $text);
        
// $this->utils->outputResult($text . '-' . $weight);
// echo "Text:$text<br>";die("Gewicht:$weight");
        
        // return the text
        return $text;

    }
    
    protected function correctNames(string &$text) : void
    {

        // remove spaces
        $text = trim($text);

        // note: changes here must be done in removeProducer() too

        // papadopoulos?
        if (substr($text, 0, 7) === 'Papad. ') $text = 'Papadopoulos ' . substr($text, 7);
        if (substr($text, 0, 6) === 'Papad.') $text = 'Papadopoulos ' . substr($text, 6);

    }

    protected function removeProducer(string &$productName) : void
    {

        // read the producer names
        $names = file_get_contents($this->utils->dataSeoDir . 'producer.txt');

        // put the producers into an array
        $producers = explode("\n", $names);

        // loop through the producer
        foreach ($producers as $producer) {

            // make sure we do not mess up with other data
            $producer .= ' ';

            // get the length of the producer name
            $len = strlen($producer);

            // get the left part of the product name
            $left = substr($productName, 0, $len);

            // found the producer name in the beginning of the product name?
            if (strcasecmp($left, $producer) === 0) {
                
                // remove the producer name
                $productName = substr($productName, $len);

                // make sure first character is in upper case
                $productName = ucfirst($productName);

            }

        }

    }

    protected function multiplyWeight(string $text, float &$weight) : void
    {
        
        // use up to 25 multiplier
        for ($x = 2; $x < 26; $x++) {
            
            // check if it must be multiplied
            if (strpos($text, strval($x . ' x')) > -1 || strpos($text, strval($x . ' Stück')) > -1) {

                // multiply with
                $weight = $weight * $x;

            }

        }

    }

    protected function getWeight(array $words, string &$word, int $index, float &$weight) : void
    {

        // note: always add the weight, since it is possible that 2
        // weight information are given in a title (e. g. 200g+50g gratis)

        // #10513 Ion Bonbons Omamy Butter 100g No7301
// echo "$word<br>";
        // do we have a weight information?
        if ($word === 'Gr') {

            // check if a valid weight information exists in the word before
            if ($this->checkWeight($words[$index], $weight, 1)) $word = 'g';

        } elseif ($word === 'Ml') {

            // check if a valid weight information exists in the word before
            if ($this->checkWeight($words[$index], $weight, 1)) $word = ' ml';

        } elseif ($word === 'Lt') {

            // check if a valid weight information exists in the word before
            if ($this->checkWeight($words[$index], $weight, 1000)) $word = ' Liter';

        } elseif ($word === 'Kg') {

            // check if a valid weight information exists in the word before
            if ($this->checkWeight($words[$index], $weight, 1000)) $word = 'kg';

        } elseif (substr($word, -1) === 'G') {

            // check if a valid weight information exists in the word before the weight unit
            if ($this->checkWeight(substr($word, 0, -1), $weight, 1)) $word = substr($word, 0, -1) . 'g';

        } elseif (substr($word, -2) === 'Gr') {

            // check if a valid weight information exists in the word before the weight unit
            if ($this->checkWeight(substr($word, 0, -2), $weight, 1)) $word = substr($word, 0, -2) . 'g';
                        
        } elseif (substr($word, -2) === 'Ml') {

            // check if a valid weight information exists in the word before the weight unit
            if ($this->checkWeight(substr($word, 0, -2), $weight, 1)) $word = substr($word, 0, -2) . ' ml';

        } elseif (substr($word, -2) === 'Lt') {
            
            // check if a valid weight information exists in the word before the weight unit
            if ($this->checkWeight(substr($word, 0, -2), $weight, 1000)) $word = substr($word, 0, -2) . ' Liter';

        } elseif (substr($word, -2) === 'Kg') {
            
            // check if a valid weight information exists in the word before the weight unit
            if ($this->checkWeight(substr($word, 0, -2), $weight, 1000)) $word = substr($word, 0, -2) . 'kg';

        }

    }

    protected function checkWeight(string $word, float &$weight, int $multiply=1) : bool
    {

        // init vars
        $found = false;

        // make sure no space are given around the word
        $word = trim($word);

        // replace comma with dot to get a numeric value
        $word = str_replace(',', '.', $word);

        // is the word numeric?
        if (is_numeric($word)) {

            // change the var
            $found = true;

            // add the weight in grams and/or kg
            $weight += $word * $multiply;

        }

        // return the result
        return $found;
        
    }

    protected function correctNumbers(string &$word) : void
    {

        // insert space after a number if no dot, comma or number is the next character
        
        // make sure the word has no spaces around it
        $word = trim($word);

        // get the first character
        $firstChar = substr($word, 0, 1);

        // get the second character
        $secondChar = substr($word, 1, 1);

        // is the first character a number not followed by a dot, comma or a number?
        if (is_numeric($firstChar) && $secondChar !== '.' && $secondChar !== ',' && ! is_numeric($secondChar)) {

            // insert a space
            $word = $firstChar . ' ' . substr($word, 1);

        }

    }

    protected function correctGrams(string &$text) : void
    {

        // replace the character after grams with a space if it is not a space and before it is a number
        // JOTIS BESAMEL SAUCE 162gr-0. 20E
        
        // $word = '162gr-0';

        // get the length of the text
        $length = strlen($text);

        // text must have a length of at least 4 characters
        if ($length < 4) return;
// echo "Len:$length<br>";
        // work with lowercase text
        $temp = strtolower($text);

        // search for 0-9gr
        for ($i=0; $i < 10; $i++) {
// echo 'i: ' . $i . 'gr<br>';
            // search for the characters
            $pos = strpos($temp, $i . 'gr');
// echo "Temp:$temp<br>";
            // found?
            if ($pos > -1) {
// echo "Pos:$pos<br>";
                // do we have another character after it?
                if ($pos < $length) {
// jotis besamel sauce 162gr-0. 20e
                    // get the text up to the search term (+3 = 'gr')
                    $textBefore = substr($text, 0, $pos + 3);
// echo "Before:$textBefore<br>";
                    // get the text after
                    $textAfter = substr($text, $pos + 4);
// echo "After:$textAfter<br>";                    
                    // insert a space
                    $text = $textBefore . ' ' . $textAfter;

                    // leave the loop
                    break;
                    
                }

            }

        }
        
        // remove space
        $text = trim($text);

    }

    protected function correctBrackets(string &$text) : void
    {

        // inserts a space before an after a bracket

        // search for opening bracket
        $bracket = strpos($text, '(');

        // found?
        if ($bracket) {

            // if character before is not a space insert one
            if (substr($text, $bracket - 1, 1) !== ' ') {
                
                // get the text before the bracket
                $textBefore = substr($text, 0, $bracket);

                // get the text from the bracket
                $textAfter = substr($text, $bracket);

                // put new text together
                $text = $textBefore . ' ' . $textAfter;
            
            }

        }

        // search for closing bracket
        $bracket = strpos($text, ')');

        // found?
        if ($bracket) {

            // if character after is not a space insert one
            if (substr($text, $bracket + 1, 1) !== ' ') {
                
                // get the text up to the bracket
                $textBefore = substr($text, 0, $bracket + 1);

                // get the text after the bracket
                $textAfter = substr($text, $bracket + 1);
                
                // put new text together
                $text = $textBefore . ' ' . $textAfter;
            
            }

        }

    }

    protected function correctSlashes(string &$text) : void
    {

        // insert spaces before and after a slash

        // search for an slash
        $slash = strpos($text, '/');

        // found?
        if ($slash) {

            // if character before is not a space insert one
            if (substr($text, $slash - 1, 1) !== ' ') {
                
                // get the text before the slash
                $textBefore = substr($text, 0, $slash);

                // get the text from the slash
                $textAfter = substr($text, $slash);

                // put new text together
                $text = $textBefore . ' ' . $textAfter;

                // correct slash position after the space was inserted
                $slash++;

                // if character after is not a space insert one
                if (substr($text, $slash + 1, 1) !== ' ') {
                    
                    // get the text up to the slash
                    $textBefore = substr($text, 0, $slash + 1);

                    // get the text after the slash
                    $textAfter = substr($text, $slash + 1);
                    
                    // put new text together
                    $text = $textBefore . ' ' . $textAfter;
                
                }

            
            }

        }

    }

    protected function correctX(string &$text) : void
    {

        // search for an x
        $x = strpos(strtolower($text), 'x');

        // found?
        if ($x) {

            // character before is a number?
            if (is_numeric(substr($text, $x - 1, 1))) {
                
                // get the text before the x
                $textBefore = substr($text, 0, $x);

                // get the text after the x
                $textAfter = substr($text, $x + 1);
                
                // put new text with lower x together
                $text = $textBefore . ' x' . $textAfter;

                // correct x position after the space was inserted
                $x++;

                // if character after is a number?
                if (is_numeric(substr($text, $x + 1, 1))) {
                    
                    // get the text up to the x
                    $textBefore = substr($text, 0, $x + 1);

                    // get the text after the x
                    $textAfter = substr($text, $x + 1);
                    
                    // put new text together
                    $text = $textBefore . ' ' . $textAfter;
                
                }
            
            }

        }

    }


    protected function correctSign(string &$text, string $sign) : void
    {

        // init vars
        $break = 0;

        // insert spaces before and after the sign

        // search for the sign
        $pos = strpos($text, $sign);

        // found?
        while ($pos) {

            // character before is not a space?
            if (substr($text, $pos - 1, 1) !== ' ') {
                
                // get the text before the sign
                $textBefore = substr($text, 0, $pos);

                // get the text after the sign
                $textAfter = substr($text, $pos);
                
                // put new text together
                $text = $textBefore . ' ' . $textAfter;

                // correct sign position after the space was inserted
                $pos++;

                // character after is not a space?
                if (substr($text, $pos + 1, 1) !== ' ') {
                    
                    // get the text up to the sign
                    $textBefore = substr($text, 0, $pos + 1);

                    // get the text after the sign
                    $textAfter = substr($text, $pos + 1);
                    
                    // put new text together
                    $text = $textBefore . ' ' . $textAfter;
                
                }
            
            }

            // increase emergency counter
            $break++;

            // item should not have more than 2 or 3 signs
            if ($break > 5) break;

            // search for the next sign (item can have multiple signs)
            $pos = strpos($text, $sign, $pos + 1);

        }

    }

    protected function correctDots(string &$text) : void
    {

        // inserts a space after a dot

        // init vars
        $insert = false; $break = 0;

        // search for a dot from
        $dot = strpos($text, '.'); $charBefore = '';

        // found?
        while ($dot) {

            // dot should not be at the beginning, but check for certain
            if ($dot > 1) {

                // get the character before the dot
                $charBefore = substr($text, $dot - 1, 1);

            }
                    
            // get the character after the dot
            $charAfter = substr($text, $dot + 1, 1);

            // character after the dot is not a space and not a number?
            if ($charAfter !== ' ' && ! is_numeric($charAfter)) $insert = true;

            // or character before is a string and character after is a number?
            // #10005 Papad. Caprice Schokorollen Clas.400g
            // Jotis Farina rot 500g.2 + 1 gratis
            if ($charBefore !== '' && is_string($charBefore) && is_numeric($charAfter)) $insert = true;

            // insert the dot?
            if ($insert) {
                
                // get the text up to the sign
                $textBefore = substr($text, 0, $dot + 1);

                // get the text after the sign
                $textAfter = substr($text, $dot + 1);
                
                // put new text together
                $text = $textBefore . ' ' . $textAfter;
            
            }

            // increase emergency counter
            $break++;

            // item should not have more than 2 or 3 dots
            if ($break > 5) break;

            // search for the next dot (item can have multiple dots)
            $dot = strpos($text, '.', $dot + 1);

        }

    }
    
    protected function correctPercent(string &$text) : void
    {

        // insert a space after a %

        // search for a %
        $percent = strpos($text, '%');

        // found?
        if ($percent) {

            // character after is not a space?
            if (substr($text, $percent + 1, 1) !== ' ') {
                
                // get the text up to the %
                $textBefore = substr($text, 0, $percent + 1);

                // get the text after the %
                $textAfter = substr($text, $percent + 1);

                // put new text together
                $text = $textBefore . ' ' . $textAfter;

            }

        }

    }

    protected function replacePiece(string &$text) : void
    {

        // St found?
        if (substr(trim($text), -2) === 'St') {

            // replace it
            $text = substr($text, 0, -2) . 'Stück';

        }

        // get the position
        $piece = strrpos($text, 'Stück');

        // found?
        if ($piece) {

            // character before is not a space?
            if (substr($text, $piece - 1, 1) !== ' ') {
                
                // get the text before the piece
                $textBefore = substr($text, 0, $piece);

                // get the text after the piece
                $textAfter = substr($text, $piece);
                
                // put new text together
                $text = $textBefore . ' ' . $textAfter;

            }

        }

    }

    protected function replaceUnits(string &$text, string $from, string $to) : void
    {

        // replace 10 units
        for ($i = 0; $i < 10; $i++) {

            // replace the unit
            $text = str_replace($i . $from, $i . $to, $text);
            $text = str_replace($i . ' ' . $from, $i . $to, $text);

        }

    }

    public function setSeoTitle(object &$koronaProduct) : void
    {

        // title should be about 60 characters and/or a width of 600 pixels

        // use a var for the product name, since we change it here
        $productName = $koronaProduct->name;

        // remove producer name from the product name
        $this->removeProducer($productName);

        // get the icon for the category
        $icon = $this->getCategoryIcon($koronaProduct->commodityGroup->name);

        // get the length of the product name
        $length = strlen($productName);

        //  | 🇬🇷 Griechische Lebensmittel online = 37 characters
        //  | 🇬🇷 Griechische Lebensmittel= 30 characters
        //  | 🇬🇷 Greek Food = 16 characters

        // very short product name?
        if ($length < 25) {

            // use icon, name and long appendix
            $seoTitle = $icon . $productName . ' | 🇬🇷 Griechische Lebensmittel online';

        // normal product name?
        } elseif ($length < 33) {

            // use icon, name and shorter appendix
            $seoTitle = $icon . $productName . ' | 🇬🇷 Griechische Lebensmittel';

        // long product name?
        } elseif ($length < 40) {
            
            // use icon, name and shortest appendix
            $seoTitle = $icon . $productName . ' | 🇬🇷 Greek Food';

        // very long product name
        } else {

            // use icon, name and no appendix
            $seoTitle = $icon . $productName;

        }

        // set the title
        $koronaProduct->seoTitle = $seoTitle;

    }

    public function setSeoDescription(object &$koronaProduct) : void
    {

        // description should be between 150 and 160 characters

        // init vars
        $seoDescription = '';

        // get the product name
        $productName = $koronaProduct->name;

        // remove producer from the product name
        $this->removeProducer($productName);

        // use icons around the product name
        $productName = '👉' . $productName . '👈';

        // slogan (52 characters)
        $slogan = 'GM Griechischer Top Markt: Alles Top...Alles lecker!';

        // phone number (26 characters)
        $phone = 'Hotline: 📞 (06134) 2965682.';

        // get the length of the title
        $length = strlen($productName);

        // get the icon for the category
        // $icon = $this->getCategoryIcon($koronaProduct->commodityGroup->name);
        
        // Leben wie in Griechenland! Griechische Lebensmittel hier online kaufen. = 71 characters
        // Leben wie in Griechenland mit unserem Onlineshop! = 49 characters
        // Leben wie in Griechenland! = 26 characters

        // do not worry about too long descriptions
        $seoDescription = 'Leben wie in Griechenland! Griechische Lebensmittel hier online kaufen. Zum Beispiel: ' . $productName . ' ' . $slogan . ' ' . $phone;

        // // short title?
        // if ($length < 50) {

        //     // use long text slogan and phone number
        //     $seoDescription = 'Leben wie in Griechenland mit unserem Onlineshop! ' . $productName . ' ' . $slogan . ' ' . $phone;
            
        // // normal title?
        // } elseif ($length < 65) {

        //     // use short text, slogan and phone number
        //     $seoDescription = 'Leben wie in Griechenland! ' . $productName . ' ' . $slogan . ' ' . $phone;
            
        // // long title?
        // } elseif ($length < 87) {

        //     // use slogan and phone number
        //     $seoDescription = $productName . ' ' . $slogan . ' ' . $phone;

        // // very long title?
        // } elseif ($length < 130) {

        //     // use phone number only
        //     $seoDescription = $productName . ' ' . $phone;

        // }

        // set the description
        $koronaProduct->seoDescription = $seoDescription;

    }

    protected function getCategoryIcon(string $category) : string
    {

        // if no category object is created, create a new one
        if ($this->categoryIcon === null) $categoryIcon = new CategoryIcon($this->utils);

        // call the method and return the icon
        return $categoryIcon->{__FUNCTION__}($category);

    }

    public function setDescription(object &$koronaProduct) : void
    {

        // set the product description
        $this->setDescriptionText($koronaProduct);

        // no description given?
        if (strlen($koronaProduct->description) < 10) {

            // set a generic description
            $koronaProduct->description = '<p>Leider ist keine nähere Beschreibung für den Artikel <b>' . $koronaProduct->name . '</b> verfügbar.</p>';

        }
        
    }

    protected function setDescriptionText(object &$koronaProduct) : void
    {

        // init vars
        $descriptionText  = '';
        
        // descriptions given?
        if (isset($koronaProduct->descriptions)) {

            // loop through the description texts
            foreach ($koronaProduct->descriptions as $text) {

                // text is type of description?
                if ($text->type === 'DESCRIPTION' && strlen($text->text) > 5) {

                    // use this text as description
                    $descriptionText = trim($text->text);

                    // leave the loop
                    break;

                }

                // text is type of receipt?
                // note: makes no sense since this is identically with the product name
                // if ($text->type === 'RECEIPT_TEXT' && strlen($text->text) > 5) {

                //     // use this text as description
                //     $descriptionText = trim($text->text);

                //     // leave the loop
                //     break;

                // }

            }
            
        }
      
        // set the description text
        $koronaProduct->description = $descriptionText;
        
    }

    public function convert2Html(object &$koronaProduct) : void
    {

        // no generic html description?
        if (strpos($koronaProduct->description, '<p>') === false) {

            // put the description in a paragraph (makes problems with mulitple updates)
            // $koronaProduct->description = '<p>' . $koronaProduct->description . '</p>';

            // make links clickable
            $this->replaceLinks($koronaProduct->description);

            // format lists
            $this->formatLists($koronaProduct->description);
            
        }

        // replace line breaks
        $koronaProduct->description =    str_replace("\r\n", '<br>', $koronaProduct->description);
        $koronaProduct->seoDescription = str_replace("\r\n", ' ',    $koronaProduct->seoDescription);
        $koronaProduct->description =    str_replace("\n", '<br>',   $koronaProduct->description);
        $koronaProduct->seoDescription = str_replace("\n", ' ',      $koronaProduct->seoDescription);

        // correct dots
        $koronaProduct->description = str_replace(' . ', '. ', $koronaProduct->description);

        // remove double spaces
        $koronaProduct->description = str_replace('  ', ' ', $koronaProduct->description);
        $koronaProduct->seoDescription = str_replace('  ', ' ', $koronaProduct->seoDescription);

    }
    protected function replaceLinks(string &$description) : void
    {

        // init vars
        $end = false;

        // define characters which stands for the end of a link
        $ends = array(',', '.', ' ', ')', '!');
        
        // split the description into lines
        $lines = explode("\n", $description);

        // reset description var
        $description = '';

        // loop through the lines
        foreach ($lines as $line) {

            // search for link
            $www = strpos($line, 'www');
            $http = strpos($line, 'http');
            
            // link in the line?
            if ($www > -1 || $http > -1) {

                // get the position
                $pos = $www > -1 ? $www : $http;

                // remove blank before a dot (looks ugly)
                $line = str_replace(' .', '.', $line);

                // find the last slash
                $pos2 = strrpos($line, '/');

                // not found?
                if ($pos2 === false) {

                    // search for a space
                    $pos2 = strpos($line, ' ', $pos);
                    
                }

                // pos 2 found?
                if ($pos2 > -1) {

                    // find the end of the link
                    for ($i = $pos2 - 1; $i < strlen($line); $i++) {

                        // get the character of this position
                        $char = substr($line, $i, 1);

                        // end character found?
                        if (in_array($char, $ends)) {

                            // set var
                            $end = true;

                            // leave the loop
                            break;

                        }

                    }

                    // end character found?
                    if ($end === true) {

                        // link is the text from the starting position to the end postion
                        $link = substr($line, $pos, $i - $pos);

                    } else {

                        // link is the whole text after the starting position
                        $link = substr($line, $pos);                    
                        
                    }

                    // protokoll missing?
                    $httpLink = $http === false ? 'https://' . $link : $link;

                    // make the link clickable
                    $line = str_replace($link, '<a href="' . $httpLink . '" rel="noopener nofollow" target="_blank" title="Homepage in neuem Register öffnen...">' . $link . '</a>', $line);

                }
                
            }

            // add line to the description
            $description .= $line . "\n";

        }

    }

    protected function formatLists(string &$description) : void
    {

        // format unsorted list
        $this->formatUnsortedList($description);

        // format sorted list
        $this->formatSortedList($description);

    }

    protected function formatUnsortedList(string &$description) : void
    {

        // init vars
        $list = false;

        // split the description into lines
        $lines = explode("\n", $description);

        // reset description
        $description = '';

        // loop through the lines
        for ($i = 0; $i < count($lines); $i++) {

            // get the current line
            $line = trim($lines[$i]);

            // end of list?
            if ($list === true) {

                // no more list items?
                if (substr($line, 0, 2) !== '- ') {

                    // reset list var
                    $list = false;

                    // close the list
                    $description .= '</ul>' . "\n" . $line . "\n";

                } else {

                    // add next list item
                    $description .= '<li>' . ltrim($line, '- ') . '</li>' . "\n";

                }

            // bullet at the beginning of this and the next line?
            } elseif ($i > 1 && substr($line, 0, 2) === '- ' && substr(trim($lines[$i + 1]), 0, 2) === '- ') {

                // set var
                $list = true;

                // start unsorted list
                $description .= '<ul>' . "\n" . '<li>' . ltrim($line, '- ') . '</li>' . "\n";

            } else {

                // add normal line to the description
                $description .= $line . "\n";

            }

        }

    }

    protected function formatSortedList(string &$description) : void
    {

        // init vars
        $list = false;

        // split the description into lines
        $lines = explode("\n", $description);

        // reset description
        $description = '';

        // loop through the lines
        for ($i = 0; $i < count($lines); $i++) {

            // get the current line
            $line = trim($lines[$i]);

            // end of list?
            if ($list === true) {

                // no more list items?
                if ( ! is_numeric(substr($line, 0, 1)) && substr($line, 1, 1) !== '.') {

                    // reset list var
                    $list = false;

                    // close the list
                    $description .= '</ol>' . "\n" . $line . "\n";

                } else {

                    // add next list item
                    $description .= '<li>' . substr($line, 2) . '</li>' . "\n";

                }

            // ordered list?
            } elseif ($i > 3 && substr($line, 0, 2) === '1.' && substr(trim($lines[$i + 1]), 0, 2) === '2.') {

                // set var
                $list = true;

                // start unsorted list
                $description .= '<ol>' . "\n" . '<li>' . substr($line, 2) . '</li>' . "\n";

            } else {

                // add normal line to the description
                $description .= $line . "\n";

            }

        }

    }

    public function setSingleDescription(string $sku) : void
    {

        // get the article from korona
        $result = $this->utils->curlExec('korona', 'GET', '', 'products?includeDeleted=false&tag=Onlineshop&number=' . $sku);

        // found?
        if (count($result->results) === 1) {

            // get the product
            $koronaProduct = $result->results[0];

            // get all shopify products
            $this->utils->shopifyUtils->getShopifyProducts();

            // find the corresponding product in shopify
            $shopifyProduct = $this->utils->shopifyUtils->findProduct($sku);

            // found?
            if ($shopifyProduct->items === 1) {

                // set the description
                $this->setDescriptionText($koronaProduct);

                // for the convert function set the seo description too
                $this->setSeoDescription($koronaProduct);

                // make it html conform
                $this->convert2Html($koronaProduct);

                // set the body
                $body = '{"product":{
                            "id":' . $shopifyProduct->id . ',
                            "body_html":"' . str_replace('"', '\u0022', $koronaProduct->description) . '"
                            }
                        }';

                // update the product
                $result = $this->utils->curlExec('shopify', 'PUT', $body, 'products/' . $shopifyProduct->id . '.json');

                var_export($result);die();
                
            } else {

                die('Shopify product not found');

            }           

            var_export($koronaProduct->description);die();

        } else {

            die('Korona article not found');

        }
        var_export($result->results);die('jasdf');

    }

}