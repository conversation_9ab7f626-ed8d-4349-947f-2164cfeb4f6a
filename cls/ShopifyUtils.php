<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.2.0
 * @create date: 2022-01-31
 * @modify date: 2022-03-11
 * @description: Provides shared methods for the Shopify api
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class ShopifyUtils
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function syncOrders() : void
    {

        // $this->utils->logError('{"message":"No new orders found in Shopify","die":true}');

        // shopify sync api needed for this function
        $class = new ShopifySync($this->utils);

        // call the method
        $class->syncOrders();

    }

    public function setOrderData(object $order) : bool
    {

        // orders class needed
        $class = new ShopifyOrders($this->utils);

        // call the function
        return $class->setOrderData($order);

    }

    public function findProduct(string $sku) : object
    {

        // use shopify product object
        $class = new ShopifyProduct($this->utils);

        // call the method
        return $class->findProduct($sku);

    }

    public function checkProduct(object $koronaProduct) : void
    {

        // use shopify product object
        $class = new ShopifyProduct($this->utils);

        // call the method
        $class->checkProduct($koronaProduct);
     /*   
        $date_time = strftime("%Y%m%d %H:%M:%S",time());
                    $fpa = fopen(dirname(__FILE__).'/temp/shopifyutils_checkProduct - '.$date_time.'.txt', 'w'); 
                    fwrite($fpa, 'Check product'."\r\n");
                    fwrite($fpa,json_encode($koronaProduct)."\r\n" );
                    fclose($fpa);
       */ 

    }

    public function updateProduct(object $koronaProduct) : void
    {

        // use shopify price update object
        $class = new ShopifyProductUpdateProduct($this->utils);

        // call the method
        $class->updateProduct($koronaProduct);
        
        /*
        $date_time = strftime("%Y%m%d %H:%M:%S",time());
                    $fpa = fopen(dirname(__FILE__).'/temp/shopifyutils_updateProduct - '.$date_time.'.txt', 'w'); 
                    fwrite($fpa, 'Update product'."\r\n");
                    fwrite($fpa, json_encode($koronaProduct)."\r\n");
                    fclose($fpa);
       */ 
        

    }

    public function createProduct(object $koronaProduct) : void
    {

        // use shopify product create object
        $class = new ShopifyProductCreate($this->utils);

        // call the method to create a new product in shopify
        $class->createProduct($koronaProduct);
    /*    
        $date_time = strftime("%Y%m%d %H:%M:%S",time());
                    $fpa = fopen(dirname(__FILE__).'/temp/shopifyutils_createProduct - '.$date_time.'.txt', 'w'); 
                    fwrite($fpa, 'Create product'."\r\n");
                    fwrite($fpa, json_encode($koronaProduct)."\r\n");
                    fclose($fpa);
    */    
    }

    public function updateStock(object $koronaProduct) : void
    {

        // use shopify product update object
        $class = new ShopifyProductUpdateStock($this->utils);

        // call the method
        $class->updateStock($koronaProduct);

    }

    public function createStockAmount(object $koronaProduct) : void
    {

        // use shopify product update object
        $class = new ShopifyProductUpdateStock($this->utils);

        // call the method
        $class->createStockAmount($koronaProduct);

    }
    
    public function updateImages(object $koronaProduct) : void
    {

        // use shopify product update images object
        $class = new ShopifyProductUpdateImages($this->utils);

        // call the function
        $class->updateImages($koronaProduct);

    }

    public function updateShopifyCollections() : void
    {

        // use collections object
        $class = new Collections($this->utils);

        // call the method
        $class->updateShopifyCollections();

    }

    public function getOrders() : void
    {

        // shopify orders api needed for this function
        $class = new ShopifyOrders($this->utils);

        // call the method
        $class->getOrders();

    }

    public function getFulfillments(string $orderId) : array
    {

        // shopify orders api needed for this function
        $class = new ShopifyOrders($this->utils);

        // call the method
        return $class->getFulfillments($orderId);

    }

    public function getRefunds(string $orderId) : array
    {

        // shopify orders api needed for this function
        $class = new ShopifyOrders($this->utils);

        // call the method
        return $class->getRefunds($orderId);

    }

    public function getRefund(string $orderId, string $refundId) : object
    {

        // shopify orders api needed for this function
        $class = new ShopifyOrders($this->utils);

        // call the method
        return $class->getRefund($orderId, $refundId);

    }

    public function getOrder(string $orderName) : object
    {

        // shopify orders api needed for this function
        $class = new ShopifyOrders($this->utils);

        // call the method
        return $class->getOrder($orderName);

    }

    public function createOrders() : void
    {

        // shopify orders api needed for this function
        $class = new ShopifyOrders($this->utils);

        // call the method
        $class->createOrders();

    }

    public function deleteOrders() : void
    {

        // shopify orders api needed for this function
        $class = new ShopifyOrders($this->utils);

        // call the method
        $class->deleteOrders();

    }

    public function getShopifyInventoryItems() : void
    {

        // use shopify inventory object
        $class = new ShopifyInventory($this->utils);

        // call the method
        $class->getShopifyInventoryItems();
        
    }

    public function getShopifyProducts(int $page=0, string $pageInfo='') : void
    {
        
        // log info
        $this->utils->logInfo('Fetching Shopify products page ' . $page . '...');

        // get the products for the online shop (default return size is 50 items, maximum size is 250 items)
        $result = $this->utils->curlExec('shopify', 'GET', '', 'products.json/?limit=250' . $pageInfo);

        // $this->utils->outputResult($result);

        // problem?
        if ( ! isset($result->products)) {

            // log error and die
            // note: changes here must be made in LockFile class too (isTempError)
            $this->utils->logError('Can not read Shopify products', $result, true);

        }
        
        // log info
        $this->utils->logInfo('Merging Shopify products...');

        // merge the result with the products array to avoid getting another array for each page in the products array
        $this->utils->shopifyProducts = array_merge($this->utils->shopifyProducts, $result->products);

        // log info
        $this->utils->logInfo('...success.');

        // another page given?
        if (isset($result->pageInfo) && strlen($result->pageInfo) > 10) {

            // increase the page counter
            $page++;

            // log info
            $this->utils->logInfo('Fetching Shopify products next page ' . $page . '...');

            // fetch the next page (< 25 prevents endless loop)
            if ($page < 25) $this->getShopifyProducts($page, $result->pageInfo);

        }

    }

    public function getLocationIdByCity(string $city='') : void
    {
        
        // init vars
        $locationId = '';

        // no city given?
        if ($city === '') {

            // get the (partial) name of the city
            $city = $this->utils->get('shopify', 'city');

        }        

        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'GET', '', 'locations.json');

        // problem?
        if ( ! isset($result->locations) || count($result->locations) === 0) {

            // log error and die
            $this->utils->logError('Can not read Shopify locations', $result, true);

        }

        // loop through the locations
        foreach ($result->locations as $location) {

            // wanted organization found?
            if (strpos(strtolower($location->city), strtolower($city)) > -1) {

                // no organization id yet found?
                if ($locationId === '') {

                    // set the id
                    $locationId = $location->id;

                } else {

                    // log error and die
                    $this->utils->logError('Can not find a unique location id for the city ' . $city . '. Please check the Shopify configuration and make sure the city name is unique.', $result, true);

                }               

            }

        }
        
        // not found?
        if ($locationId === '') {

            // log error and die
            $this->utils->logError('Location id for the city ' . $city . ' not found.', $result, true);

        }

        // log info
        $this->utils->logInfo('Shopify location id ' . $locationId);

        // set the id
        $this->utils->shopifyLocationId = $locationId;

    }

    public function getVariantId(string $productId) : string
    {

        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'GET', '', 'products/' . $productId . '.json');

        // show the variant id
        $this->utils->outputResult($result->product->variants[0]->id);

    }

    public function getProductById(string $productId) : object
    {

        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'GET', '', 'products/' . $productId . '.json');

        // show the variant id
        $this->utils->outputResult($result->product);

    }

    public function updateMetafield($metafieldId, $value) : void
    {

        // metafields class needed
        $class = new MetaFields($this->utils);

        // call the function
        $class->updateMetaField($metafieldId, $value);

    }

}