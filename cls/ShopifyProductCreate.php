<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.2.1
 * @create date: 2022-01-07
 * @modify date: 2022-03-02
 * @description: Creates a product in Shopify with images, tags and categories
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class ShopifyProductCreate
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function createProduct(object $koronaProduct) : void
    {
       /* 
        $date_time = strftime("%Y%m%d %H:%M:%S",time());
                    $fpa = fopen(dirname(__FILE__).'/temp/shopifyProductCreate_createProduct - '.$date_time.'.txt', 'w'); 
                    fwrite($fpa, 'Update product');
                    fwrite($fpa, $koronaProduct);
                    fwrite($fpa,$koronaProduct->description);
                    fwrite($fpa,$koronaProduct->ingredients);
                    fclose($fpa);
        */
        
        // get the product number
        $sku = $koronaProduct->number;
        // $this->utils->outputResult($sku);
        // if ( ! isset($koronaProduct->image->id)) return;
        // if ($sku !== '10088') return;

        // set article info
        $article = '#' . $sku . ' (' . $koronaProduct->name . ')';

        // log info
        $this->utils->logInfo('Article ' . $article . ' does not exists in Shopify');

    // $this->utils->outputResult($koronaProduct);

        // set description and seo stuff
        $this->utils->koronaUtils->setSeo($koronaProduct);

        // use images object
        $class = new Images($this->utils);

        // set the images (after seo stuff)
        $class->setImages($koronaProduct);

        // get images
        $images = isset($koronaProduct->shopifyImages) ? implode(',', $koronaProduct->shopifyImages) : '';

        // no images but images are needed?
        if (strlen($images) < 30 && $this->utils->get('shopify', 'needImage') === true) {

            // log info
            $this->utils->logInfo('Skipped article without image');

            // nothing more to do here
            return;

        }

        // set the product tags
        $this->setTags($koronaProduct);   
        
        // add all groups to the korona product, since it contains only the lowest group
        $this->addGroups($koronaProduct);

        // set the stock amount
        $stockAmount = isset($koronaProduct->stockAmount) ? intval($koronaProduct->stockAmount) : 0;

        // set the product status
        $status = round(floatval($koronaProduct->price), 1) == 0 ||
                  round(floatval($koronaProduct->weight), 1) == 0 ||
                  strpos($koronaProduct->name, 'gratis') > - 1 ||
                  strpos($koronaProduct->name, 'Glas ') > - 1 ? 'draft' : 'active';

        // images needed for new products?
        if ($this->utils->get('shopify', 'needImage') === true && $images === '') $status = 'draft';

        // set the weight unit
        if ($koronaProduct->weight > 999) {

            // set weight unit to kilograms
            $weightUnit = 'kg';

            // change the grams to kilograms
            $koronaProduct->weight = round($koronaProduct->weight / 1000, 2);

        } else {

            // set weight unit to grams
            $weightUnit = 'g';

        }

        // set the body to create a new product
        // note: requires_shipping is deprecated
        // note: setting the price per unit is not possible with rest and/or graphql api:
        //       https://community.shopify.com/c/shopify-apis-and-sdks/cannot-find-unit-price-and-unit-price-measurement-data-via-the/m-p/1197724
        $body = '{
            "product": {
                "title": "' . $koronaProduct->name . '",
                "handle": "' . $koronaProduct->handle . '",
                "body_html": "' . $koronaProduct->description . '",
                "product_type": "' . $koronaProduct->commodityGroup->name . '",
                "tags": "' . $koronaProduct->shopifyTags . '",
                "status": "' . $status . '",
                "published": "true",
                "published_scope": "web",
                "metafields_global_title_tag":"' . $koronaProduct->seoTitle . '",
                "metafields_global_description_tag":"' . $koronaProduct->seoDescription . '",
                "images":[' . $images  . '],
                "variants": [
                    {
                        "price": "' . $koronaProduct->price . '",
                        "sku": "' . $sku . '",
                        "weight": "' . $koronaProduct->weight . '",
                        "weight_unit":"' . $weightUnit . '",
                        "inventory_management": "shopify",
                        "inventory_quantity": "' . $stockAmount . '",
                        "inventory_policy": "deny",
                        "requires_shipping": "true"
                    }
                ]
            }
        }';

        // log info
        // $this->utils->logInfo('Body for Shopify: ' . $body);

        // execute the curl request
        $result = $this->utils->curlExec('shopify', 'POST', $body, 'products.json');
        
        // check if the creation was successfully
        if (isset($result->product->title) && $result->product->title === $koronaProduct->name) {

            // add the created product to the korona product to be able to update the stock amount
            $koronaProduct->shopifyProduct = $result->product;
            
            // add collections
            $this->addCollections($koronaProduct, $result->product);

            // update counter
            $this->utils->created++;
            
            // log info
            $this->utils->logInfo('Article ' . $article . ' created in shopify', null, true);

        } else {

        // $this->utils->outputResult($result);
            // log error
            $this->utils->logInfo('Could not create article ' . $article . ' in shopify', $koronaProduct);
            $this->utils->logInfo('Could not create article ' . $article . ' in shopify', $result);

        }

    }

    protected function addGroups(object &$koronaProduct) : void
    {
        
        // init vars
        $groups = [];
        
        // get the groups for this product
        $this->addGroup($koronaProduct->commodityGroup->id, $groups);

        // add them to the korona product
        $koronaProduct->shopifyGroups = $groups;

    }

    protected function addGroup(string $groupId, array &$groups) : array
    {

        // search for the group in the groups array
        $index = array_search($groupId, array_column($this->utils->koronaGroups, 'id'));

        // get the article object
        $group = $this->utils->koronaGroups[$index];

        // group found?
        if (isset($group->name)) {

            // extend the var
            $groups[] = $group->name;

            // prevent endless loop
            if (count($groups) > 10) return $groups;

            // parent group exists?
            if (isset($group->parentCommodityGroup)) {

                // recall this method
                $this->addGroup($group->parentCommodityGroup->id, $groups);

            }

        }

        // return the group(s)
        return $groups;

    }

    protected function addCollections(object $koronaProduct, object $shopifyProduct) : void
    {

        // loop through the commodity groups
        foreach ($koronaProduct->shopifyGroups as $group) {
            
            // find the group in the collections array
            $collection = $this->findCollection($group);
                            
            // group found?
            if ($collection) {
                
                // add it to the product
                $this->addCollection($koronaProduct, $shopifyProduct, $collection);

            }

        }

        // add tax groups
        $this->addTaxCollection($koronaProduct, $shopifyProduct);

    }

    protected function findCollection(string $groupName) : object
    {

        // init vars
        $collection = new \stdClass();

        // find the group in the collections array
        foreach ($this->utils->shopifyCollections as $collection) {
                
            // group found?
            if ($collection->title === $groupName) {
                
                // leave the loop
                break;

            }

        }

        // return the result
        return $collection;

    }

    protected function addTaxCollection(object $koronaProduct, object $shopifyProduct) : void
    {

        // search for tax collection (see Collections class)
        $collection = $this->findCollection($koronaProduct->sector->name);

        // found?
        if ($collection) {

            // add the tax collection
            $this->addCollection($koronaProduct, $shopifyProduct, $collection);
        
        } else {

            // increase warning counter
            $this->utils->warnings++;

            // log warning
            $this->utils->logInfo('Warning: Could not find sector "' . $koronaProduct->sector->name . '" in shopify collections', null, true);

        }

    }

    protected function addCollection(object $koronaProduct, object $shopifyProduct, object $collection) : void
    {

        // log info
        $this->utils->logInfo('Adding collection "' . $collection->title . '" to shopify product with sku ' . $koronaProduct->number . '...');

        // get the id of the shopify product
        $productId = $shopifyProduct->id;
        
        // set the body for the request
        $body = '{
            "collect":{
                "product_id":' . $productId . ',
                "collection_id":' . $collection->id . '
            }
        }';
        
        // add the custom collection to the product
        $result = $this->utils->curlExec('shopify', 'POST', $body, 'collects.json');
        
        // problem?
        if ( ! isset($result->collect->collection_id)) {

            // log error
            $this->utils->logError('...failed', $result, false);

        } else {

            // log success
            $this->utils->logInfo('...success.');

        }

        // $this->utils->outputResult($result);
    }

    protected function setTags(object &$koronaProduct) : void
    {

        // init vars
        $tags  = '';

        // no tags given?
        if ( ! isset($koronaProduct->tags)) return;

        // loop through the tags
        foreach ($koronaProduct->tags as $tag) {

            // skip online shop tag, which is useless in shopify
            // this only marks products in korona for the online shop
            if (strpos(strtolower($tag->name), 'online') > -1) continue;
            
            // add the tag name to the tag list
            $tags .= $tag->name . ',';
            
        }

        // remove the last comma
        $tags = trim($tags, ',');
        
        // no tags given?
        if (strlen($tags) < 10) {

            // set the commodity group as tag
            $tags = $koronaProduct->commodityGroup->name;

        }
                
        // set the tags
        $koronaProduct->shopifyTags = strtolower($tags);

    }

}