<?php

/*
 * @author:      <PERSON> a r c h e r t 
 * @copyright:   <PERSON> a r c h e r t
 * @website:     https://www.freelancer.com/u/petermarchert
 * @license:     This software is licensed to GM Griechische Top Markt GmbH
 * @disclaimer:  This software is provided "AS IS" without any kind of warranty
 * @version:     1.1.0
 * @create date: 2022-01-07
 * @modify date: 2022-03-02
 * @description: Read first and only product variant from Shopify and get with the stock id from it the
 *               stock amount from Shopify. Then compare it with the stock amount of the Korona article.
 *               If different, update the stock amount in Shopify.
 */

 // no direct access
defined('GM_EXEC') or die('Access denied');

class ShopifyProductUpdateStock
{

    // utils object
    protected $utils = null;

    public function __construct(object $utils)
    {

        // update class vars
        $this->utils = $utils;

    }

    public function updateStock(object $koronaProduct) : void
    {
        
        // problem with number?
        if ( ! isset($koronaProduct->number)) {

            // log info
            $this->utils->logInfo('No product number found in korona product');

            // nothing more to do here
            return;

        }
        
        // coupon code does not have inventory tracking enabled
        if (strpos(strtolower($koronaProduct->name), 'gutschein') > -1) {

            // log info
            $this->utils->logInfo('Skipped inventory item for coupon code (not tracked in shopify)');

            // nothing more to do here
            return;

        }

        // get the sku
        $sku = $koronaProduct->number;

        // get the shopify product set in shopify product class
        $shopifyProduct = $koronaProduct->shopifyProduct;

        // no shopify product found?
        if ( ! isset($shopifyProduct)) {

            // log the error and die
            $this->utils->logError('Can not read shopify product data for sku ' . $sku, null, true);

        }

        // get the first variant (should be only one)
        $variant = $shopifyProduct->variants[0];

        // problem?
        if ( ! isset($variant->inventory_item_id)) {

            // log error and die
            $this->utils->logError('Can not read shopify product variant for sku ' . $sku, null, true);

        }
        
        // with the inventory item id of the variant get the inventory data
        $inventoryItem = $this->getInventoryItemById($sku, $variant->inventory_item_id);
        
        // problem?
        if (empty($inventoryItem)) return;

        // get the stock amount buffer, which makes sure Shopify can not sell products which are not available
        $stockBuffer = intval($this->utils->get('shopify', 'stockBuffer'));

        // stock buffer must be positive
        if ($stockBuffer < 0) $stockBuffer = $stockBuffer * -1;

        // calculate shopify amount
        $shopifyAvailable = intval($inventoryItem->available);

        // get korona stock amount
        $koronaStockAmount = intval($koronaProduct->stockAmount);

        // stock amount up to date?
        // note: if the difference is exactly two times of the stock buffer than we have also not to update the amount,
        // since it will not change:
        // before update:
        // stock buffer: 5
        // value in shopify: 14
        // compare value: 14 - stockbuffer = 9
        // value in korona: 19
        // after update:
        // value in shopify: 19 - stockbuffer = 14
        if ($shopifyAvailable === ($koronaStockAmount - $stockBuffer) || ($koronaStockAmount - $shopifyAvailable) == 2 * $stockBuffer) {

            // log info
            $this->utils->logInfo('Stock amount for product #' . $sku . ' (' . $koronaProduct->name . ') is already up to date (' . $koronaProduct->stockAmount . ' - buffer of ' . $stockBuffer . ' items)');

        } else {

            // log info
            $this->utils->logInfo('Updating stock amount for product #' . $sku . ' (' . $koronaProduct->name . ')');

            // log info
            // $this->utils->logInfo('Korona: ' . $koronaStockAmount . ' | Shopify: ' . $shopifyAvailable);

            // $this->utils->logInfo('Korona - Shopify = ' . ($koronaStockAmount - $shopifyAvailable) . ' - 2x Stockbuffer = ' . (2 * $stockBuffer));

            // update the inventory item
            $this->updateInventoryItem($inventoryItem, $koronaProduct, $stockBuffer);        
        
        }

	}

    protected function updateInventoryItem(object $inventoryItem, object $koronaProduct, int $stockBuffer) : void
    {
    
        // get the stock amounts
        $koronaAmount = intval($koronaProduct->stockAmount);
        $shopifyOldAmount = intval($inventoryItem->available);
        
        // set the body to update the amount
        $body = '{
            "location_id": ' . $inventoryItem->location_id . ',
            "inventory_item_id": ' . $inventoryItem->inventory_item_id . ',
            "available": ' . intval($koronaAmount - $stockBuffer) . '
          }';

        // update the inventory amount (returns an inventory item)
        $updatedInventoryItem = $this->utils->curlExec('shopify', 'POST', $body, 'inventory_levels/set.json');

        // problem?
        if ( ! isset($updatedInventoryItem->inventory_level)) {

            // log error and die
            $this->utils->logError('Can not set inventory level for product #' . $koronaProduct->number . ' (' . $koronaProduct->name . ')', $updatedInventoryItem, true);

        }

        // update ok?
        if (intval($updatedInventoryItem->inventory_level->available) === intval($koronaAmount - $stockBuffer)) {

            // update counter
            $this->utils->stocks++;

            // log info
            $this->utils->logInfo('Stock amount for product #' . $koronaProduct->number . ' (' . $koronaProduct->name . ') set to ' . ($koronaAmount - $stockBuffer) . ' in Shopify (old value was ' . $shopifyOldAmount . ')', null, true);

        } else {

            // increase warning counter
            $this->utils->warnings++;
            
            // log info
            $this->utils->logInfo('Warning: Stock amount for product #' . $koronaProduct->number . ' (' . $koronaProduct->name . ') NOT set to ' . ($koronaAmount - $stockBuffer) . ' in Shopify (current value is still ' . $shopifyOldAmount . ')', null, true);

        }

    }

    protected function getInventoryItemById(string $sku, int $id) : object
    {

        // init vars
        $inventoryItem = new \stdClass();

        // get the inventory index
        // $index = array_search($id, array_column($this->utils->shopifyInventoryItems, 'inventory_item_id'));
        
        // get the inventory item
        // $inventoryItem = $this->utils->shopifyInventoryItems[$index];
                
        // loop through the inventory items
        foreach ($this->utils->shopifyInventoryItems as $inventoryItem) {

            // if inventory item id was found leave the loop
            if ($inventoryItem->inventory_item_id === $id) break;
            
        }

        // problem?
        if ( ! isset($inventoryItem) || $inventoryItem->inventory_item_id !== $id) {

            // log error
            $this->utils->logInfo('Can not read inventory item by its id ' . $id . ' for sku ' . $sku);

            // return empty object
            return new \stdClass();

        }

        // return the item
        return $inventoryItem;

    }
 
    public function createStockAmount(object $koronaProduct) : void
    {

        // get the inventory item id
        $inventoryItemId = $koronaProduct->shopifyProduct->variants[0]->inventory_item_id;
         
        // get the stock amount buffer, which makes sure Shopify can not sell products which are not available
        $stockBuffer = intval($this->utils->get('shopify', 'stockBuffer'));

        // stock buffer must be positive
        if ($stockBuffer < 0) $stockBuffer = $stockBuffer * -1;

        // calculate the shopify stock amount
        $stockAmount = $koronaProduct->stockAmount - $stockBuffer;

        // set the body to create an inventory level item
        $body = '{
                    "location_id":' . $this->utils->shopifyLocationId . ',
                    "inventory_item_id":' . $inventoryItemId . ',
                    "available":' . intval($stockAmount) . '
                 }';


        // set the inventory amount (returns the inventory item)
        $inventoryItem = $this->utils->curlExec('shopify', 'POST', $body, 'inventory_levels/set.json');

        // success?
        // no === here
        if (isset($inventoryItem->inventory_level) && $inventoryItem->inventory_level->available == $stockAmount) {

            // increase counter
            $this->utils->stocks++;

            // log info
            $this->utils->logInfo('Stock amount set to ' . $stockAmount . ' in Shopify for product #' . $koronaProduct->number . ' (' . $koronaProduct->name . ')', null, true);

        } else {

            // increase warning counter
            $this->utils->warnings++;

            // log info
            $this->utils->logInfo('Warning: Could not set stock amount to ' . $stockAmount . ' in Shopify for product #' . $koronaProduct->number . ' (' . $koronaProduct->name . ')', null, true);

        }

    }

}